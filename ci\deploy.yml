parameters:
# Agent pool to use for deployment
- name: pool
  type: string
  default: 'Azure Pipelines'
# Name of the stage (e.g. DeployToDev)
- name: stageName
  type: string
  default: ''
# Target environment (e.g. dev)
- name: environment
  type: string
  default: ''
# Stage dependency
- name: dependsOn
  type: string
  default: ''
# Deployment condition
- name: condition
  type: string
  default: ''
# Azure service connection name
- name: serviceConnection
  type: string
  default: ''
# SQL Server name
- name: serverName
  type: string
  default: ''
# Database name
- name: databaseName
  type: string
  default: ''
# Web app name
- name: webAppName
  type: string
  default: ''
# Resource group name
- name: resourceGroup
  type: string
  default: ''
# Deployment slot name
- name: slotName
  type: string
  default: ''
# Whether to retain this pipeline run
- name: retain
  type: boolean
  default: false

stages:
- stage: ${{ parameters.stageName }}
  dependsOn: ${{ parameters.dependsOn }}
  condition: ${{ parameters.condition }}
  variables:
    Environment: ${{ parameters.environment }}
  jobs:
  - deployment: Deploy
    pool:
      name: ${{ parameters.pool }}
    environment:
        name: ${{ parameters.environment }}
    strategy:
      runOnce:
        deploy:
          steps:
          - task: SqlAzureDacpacDeployment@1
            displayName: 'Azure SQL SqlTask - Migration Script'
            inputs:
              azureSubscription: '${{ parameters.serviceConnection }}'
              AuthenticationType: servicePrincipal
              ServerName: '${{ parameters.serverName }}'
              DatabaseName: '${{ parameters.databaseName }}'
              deployType: SqlTask
              SqlFile: '$(Pipeline.Workspace)/netprotrust-api/SQL/migrationscript.sql'

          - task: SqlAzureDacpacDeployment@1
            displayName: 'Azure SQL SqlTask - Manual Tables'
            inputs:
              azureSubscription: '${{ parameters.serviceConnection }}'
              AuthenticationType: servicePrincipal
              ServerName: '${{ parameters.serverName }}'
              DatabaseName: '${{ parameters.databaseName }}'
              deployType: SqlTask
              SqlFile: '$(Pipeline.Workspace)/netprotrust-api/SQL/CreateManualTables.sql'

          - task: AzureRmWebAppDeployment@4
            displayName: 'Azure App Service Deploy: ${{ parameters.webAppName }}'
            inputs:
              azureSubscription: '${{ parameters.serviceConnection }}'
              appType: webAppLinux
              WebAppName: '${{ parameters.webAppName }}'
              deployToSlotOrASE: true
              ResourceGroupName: '${{ parameters.resourceGroup }}'
              SlotName: '${{ parameters.slotName }}'
              packageForLinux: '$(Pipeline.Workspace)/netprotrust-api/NetProGroup.Trust.API.zip'
              RuntimeStack: 'DOTNETCORE|8.0'
              StartupCommand: 'dotnet NetProGroup.Trust.API.dll'
              
  - template: retention-template.yml
    parameters:
      condition: ${{ parameters.retain }}