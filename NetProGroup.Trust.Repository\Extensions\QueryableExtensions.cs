using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Query;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Application.Contracts.BoDir;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.Domain.Shared.Defines;
using System.Linq.Expressions;
using System.Reflection;

namespace NetProGroup.Trust.Domain.Repository.Extensions
{
    /// <summary>
    /// Extensions for IQueryable.
    /// </summary>
    public static class QueryableExtensions
    {
        /// <summary>
        /// Includes the specified includeExpression if the condition is true.
        /// </summary>
        /// <param name="query">The query.</param>
        /// <param name="condition">The condition.</param>
        /// <param name="includeExpression"> The include expression.</param>
        /// <param name="thenincludeExpression">The Theninclude expression.</param>
        /// <typeparam name="TEntity">The type of the entity.</typeparam>
        /// <typeparam name="TProperty">The type of the property.</typeparam>
        /// <returns>It returns <see cref="IQueryable{TEntity}"/>.</returns>
        public static IQueryable<TEntity> IncludeIf<TEntity, TProperty, TNextProperty>(
            this IQueryable<TEntity> query,
            bool condition,
            Expression<Func<TEntity, IEnumerable<TProperty>>> includeExpression,
            Expression<Func<TProperty, TNextProperty>> thenIncludeExpression)
            where TEntity : class
            where TProperty : class
        {
            return condition
                ? query.Include(includeExpression).ThenInclude(thenIncludeExpression)
                : query;
        }

        /// <summary>
        /// Includes the specified property if the condition is true.
        /// </summary>
        /// <param name="query"> The query. </param>
        /// <param name="condition"> The condition. </param>
        /// <param name="includeExpression"> The include expression. </param>
        /// <typeparam name="TEntity">The type of the entity.</typeparam>
        /// <typeparam name="TPreviousProperty">The type of the previous property.</typeparam>
        /// <typeparam name="TProperty">The type of the property.</typeparam>
        /// <returns> The <see cref="IIncludableQueryable{TEntity, TProperty}"/>. </returns>
        public static IIncludableQueryable<TEntity, TProperty> ThenIncludeIf<TEntity, TPreviousProperty, TProperty>(
            this IIncludableQueryable<TEntity, TPreviousProperty> query,
            bool condition,
            Expression<Func<TPreviousProperty, TProperty>> includeExpression)
            where TEntity : class
            where TPreviousProperty : class
        {
            return condition
                ? query.ThenInclude(includeExpression)
                : (IIncludableQueryable<TEntity, TProperty>)query;
        }

        /// <summary>
        /// Sorts the query by the specified sorting info.
        /// </summary>
        /// <param name="query">The query to sort.</param>
        /// <param name="sortingInfo">The sorting info.</param>
        /// <param name="sortDictionary">The sort dictionary.</param>
        /// <param name="defaultSort">The default sort.</param>
        /// <typeparam name="TEntity">The type of the entity.</typeparam>
        /// <returns>The sorted query.</returns>
        /// <exception cref="BadRequestException">Thrown when the sorting info is invalid.</exception>
        public static IQueryable<TEntity> Sort<TEntity>(this IQueryable<TEntity> query,
            SortingInfo sortingInfo,
            Dictionary<string, Expression<Func<TEntity, object>>> sortDictionary,
            Expression<Func<TEntity, object>> defaultSort) where TEntity : class
        {
            ArgumentNullException.ThrowIfNull(query, nameof(query));
            ArgumentNullException.ThrowIfNull(sortingInfo, nameof(sortingInfo));

            Expression<Func<TEntity, object>> keySelector;
            if (Framework.Extensions.StringExtensions.IsNullOrWhiteSpace(sortingInfo.SortBy))
            {
                keySelector = defaultSort;
            }
            else if (!sortDictionary.ToDictionary(pair => pair.Key.ToUpperInvariant(), pair => pair.Value).TryGetValue(sortingInfo.SortBy.ToUpperInvariant(), out keySelector))
            {
                throw new BadRequestException(ApplicationErrors.INVALID_SORTBY.ToErrorCode(), $"{sortingInfo.SortBy} is not a valid column to sort by.");
            }

            return sortingInfo.SortOrder == OrderByDefines.Ascending ? query.OrderBy(keySelector) : query.OrderByDescending(keySelector);
            ;
        }

        /// <summary>
        /// Sorts the query by the specified sorting info, using reflection to get the sortable columns.
        /// Overrides to the sorting can be specified per column.
        /// </summary>
        /// <param name="query">The query to sort.</param>
        /// <param name="sortingInfo">The sorting info.</param>
        /// <param name="overrides">The overrides to the default sorting by property.</param>
        /// <param name="defaultSort">The default sort.</param>
        /// <typeparam name="TEntity">The type of the entity.</typeparam>
        /// <typeparam name="TSearchDTO">The type of the search DTO</typeparam>
        /// <returns>The sorted query.</returns>
        /// <exception cref="BadRequestException">Thrown when the sorting info is invalid.</exception>
        public static IQueryable<TEntity> SortBySpecification<TEntity, TSearchDTO>(this IQueryable<TEntity> query,
            SortingInfo sortingInfo,
            Dictionary<string, Expression<Func<TEntity, object>>> overrides, // TODO make overrides optional
            Expression<Func<TEntity, object>> defaultSort) where TEntity : class
        {
            ArgumentNullException.ThrowIfNull(query, nameof(query));
            ArgumentNullException.ThrowIfNull(sortingInfo, nameof(sortingInfo));

            var sortableColumnsProperty = typeof(TSearchDTO).GetProperties().SingleOrDefault(info =>
            {
                var sortableColumnsAttribute = info.GetCustomAttribute<SortableColumnsAttribute>();
                return sortableColumnsAttribute != null;
            });

            if (sortableColumnsProperty == null)
            {
                throw new BadRequestException(ApplicationErrors.INVALID_SORTBY.ToErrorCode(), "No sortable columns found.");
            }

            var sortableColumns = sortableColumnsProperty.GetCustomAttribute<SortableColumnsAttribute>()!.SortableColumns;

            var sortDictionary = sortableColumns.ToDictionary(propertyName => propertyName.ToUpperInvariant(),
                propertyName =>
                {
                    if (overrides.TryGetValue(propertyName, out var overrideExpression))
                    {
                        return overrideExpression;
                    }

                    var entityType = typeof(TEntity);
                    if (entityType.GetProperty(propertyName) == null)
                    {
                        throw new BadRequestException(ApplicationErrors.INVALID_SORTBY.ToErrorCode(), $"Property {propertyName} does not exist on type {entityType.Name}.");
                    }

                    var parameter = Expression.Parameter(entityType, "x");
                    var propertyAccess = Expression.Property(parameter, propertyName);
                    var converted = Expression.Convert(propertyAccess, typeof(object));

                    return Expression.Lambda<Func<TEntity, object>>(converted, parameter);
                });

            return query.Sort(sortingInfo, sortDictionary, defaultSort);
        }
    }
}