using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD.Annual;

namespace NetProGroup.Trust.Tests.Reports.Nevis.ExportSubmissionIRD.Annual
{
    [TestFixture()]
    public class Export2022SubmissionsIRDGeneratorTests : BaseExportSubmissionsIRDGeneratorTests<IExport2022SubmissionsIRDGenerator>
    {
        public override void Setup()
        {
            base.Setup();

            // Set the financial year for 2022
            FinancialYear = 2022;
        }

        /// <summary>
        /// Tests that when the TaxResidentResidentCountry field is empty, the exported Excel file
        /// shows "No Country" in the appropriate cell (Question 1.2).
        /// </summary>
        [Test]
        public async Task Question_1_2_EmptyTaxResidentCountry_ReturnsNoCountry()
        {
            // For 2022, the country value is in cell (3, 50)
            await TestEmptyTaxResidentCountryReturnsNoCountry(3, 50);
        }
    }
}
