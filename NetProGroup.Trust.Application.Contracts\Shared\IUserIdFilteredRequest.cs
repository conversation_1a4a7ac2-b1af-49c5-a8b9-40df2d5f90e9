// <copyright file="IUserIdFilteredRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Shared
{
    /// <summary>
    /// Represents a request that is filtered by a user ID.
    /// </summary>
    public interface IUserIdFilteredRequest
    {
        /// <summary>
        /// Gets or sets the user ID to filter the request by.
        /// </summary>
        Guid UserId { get; set; }
    }
}