﻿// <copyright file="IUsersDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Configuration;
using NetProGroup.Framework.DependencyInjection.Interfaces;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.Application.Contracts.Users;
using NetProGroup.Trust.Application.Contracts.Users.TermsConditions;
using NetProGroup.Trust.DataManager.Users.RequestResponses;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.Users
{
    /// <summary>
    /// Interface for the UserDataManager.
    /// </summary>
    public interface IUsersDataManager : IScopedService
    {
        /// <summary>
        /// Gets the configuration instance.
        /// </summary>
        IConfiguration Configuration { get; }

        /// <summary>
        /// Gets the Terms and Conditions acceptance status for a user.
        /// </summary>
        /// <param name="userId">ID of the user to get the status for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the acceptance status.</returns>
        Task<TermsConditionsStatusDTO> GetTermsConditionsStatusAsync(Guid userId);

        /// <summary>
        /// Sets the Terms and Conditions acceptance status for a user.
        /// </summary>
        /// <param name="request">The request with the parameters to accept the terms.</param>
        /// <returns>A task that represents the asynchronous operation.</returns>
        Task AcceptTermsConditionsAsync(AcceptTermsConditionsRequest request);

        /// <summary>
        /// Gets the method to use for MFA.
        /// </summary>
        /// <param name="userId">Id of the user to get the setting for.</param>
        /// <returns>The found method.</returns>
        Task<string> GetMFAMethodAsync(Guid userId);

        /// <summary>
        /// Sets the method to use for MFA.
        /// </summary>
        /// <param name="userId">Id of the user to get the setting for.</param>
        /// <param name="value">The method to use.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SetMFAMethodAsync(Guid userId, string value);

        /// <summary>
        /// Resets the MFA info for the given user.
        /// </summary>
        /// <param name="userId">Id of the user to reset the MFA for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task ResetMFAMethodAsync(Guid userId);

        /// <summary>
        /// Gets an attribute for the given user.
        /// </summary>
        /// <param name="userId">Id of the user to set the attribute for.</param>
        /// <param name="key">Key of the attribute to set.</param>
        /// <param name="default">Default value of the attribute if not foudn in the database.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation returning a string.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Naming", "CA1716:Identifiers should not match keywords", Justification = "Is internal")]
        Task<string> GetAttributeValueAsync(Guid userId, string key, string @default = null);

        /// <summary>
        /// Gets the value of an attribute for the given user.
        /// </summary>
        /// <typeparam name="TPropType">The type of the value to return.</typeparam>
        /// <param name="userId">Id of the user to get the attribute for.</param>
        /// <param name="key">Key of the attribute to get.</param>
        /// <param name="default">Option default value to return if attribute does not exist.</param>
        /// <returns>The value of the attribute or the default as TPropType.</returns>
        [System.Diagnostics.CodeAnalysis.SuppressMessage("Naming", "CA1716:Identifiers should not match keywords", Justification = "Is internal")]
        Task<TPropType> GetAttributeValueAsync<TPropType>(Guid userId, string key, TPropType @default = default(TPropType));

        /// <summary>
        /// Sets an attribute for the given user.
        /// </summary>
        /// <param name="userId">Id of the user to set the attribute for.</param>
        /// <param name="key">Key of the attribute to set.</param>
        /// <param name="value">Value of the attribute. </param>
        /// <param name="saveChanges">Indicates whether to save the changes.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SetAttributeValueAsync(Guid userId, string key, string value, bool saveChanges = false);

        /// <summary>
        /// Sets an attribute for the given user.
        /// </summary>
        /// <typeparam name="TPropType">The type of the value to set.</typeparam>
        /// <param name="userId">Id of the user to set the attribute for.</param>
        /// <param name="key">Key of the attribute to set.</param>
        /// <param name="value">Value of the attribute. </param>
        /// <param name="saveChanges">Indicates whether to save the changes.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SetAttributeValueAsync<TPropType>(Guid userId, string key, TPropType value, bool saveChanges = false);

        /// <summary>
        /// Gets the info about MFA for the given user.
        /// </summary>
        /// <param name="request">The request with the parameters to get the info.</param>
        /// <returns>A <see cref="Task{GetUserMFAResponse}"/> representing the asynchronous operation.</returns>
        Task<GetUserMFAResponse> GetUserMFAInfoAsync(GetUserMFARequest request);

        /// <summary>
        /// Verifies the generated response code.
        /// </summary>
        /// <param name="request">The request holding the user and code.</param>
        /// <returns>The result of the verification.</returns>
        Task<VerifyUserMFAResponse> VerifyUserMFAResponseAsync(VerifyUserMFARequest request);

        /// <summary>
        /// Handles a request for reset of the MFA of the user.
        /// </summary>
        /// <remarks>
        /// This will create a code to send it to the user.
        /// The user must confirm this code. See ConfirmMFARequestAsync().
        /// </remarks>
        /// <param name="request">The request holding the user.</param>
        /// <returns>A response on the request.</returns>
        Task<MFAResetResponse> RequestMFAResetAsync(MFAResetRequest request);

        /// <summary>
        /// Handles the confirmation for reset of the MFA of the user.
        /// </summary>
        /// <remarks>
        /// This will check the entered code.
        /// </remarks>
        /// <param name="request">The request holding the user and code.</param>
        /// <returns>A response on the confirmation.</returns>
        Task<ConfirmMFAResetResponse> ConfirmMFAResetAsync(ConfirmMFAResetRequest request);

        /// <summary>
        /// Updates the objectId of the use with the given email address, but only when ObjectId is not set yet.
        /// </summary>
        /// <param name="userId">The id of the user to update the ObjectId for.</param>
        /// <param name="objectId">The ObjectId to set.</param>
        /// <returns>A <see cref="Task{ApplicationUserDTO}"/> representing the asynchronous operation holding the updated user.</returns>
        Task<ApplicationUserDTO> UpdateUserObjectIdAsync(Guid userId, Guid objectId);

        /// <summary>
        /// Create a new user.
        /// </summary>
        /// <param name="createUserModel">The data for the user to create.</param>
        /// <returns>A <see cref="Task{ApplicationUserDTO}"/> representing the asynchronous operation holding the created user.</returns>
        Task<ApplicationUserDTO> CreateUserAsync(CreateUserDTO createUserModel);

        /// <summary>
        /// Gets a paged list of users with optionally additional details.
        /// </summary>
        /// <param name="request">The request with parameters for the list.</param>
        /// <returns>The paged list of users.</returns>
        Task<IPagedList<ListApplicationUsersDTO>> ListUsersAsync(UsersRequestDTO request);

        /// <summary>
        /// Retrieves the details of a user by their unique identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the user.</param>
        /// <returns>
        /// An <see cref="PCPApplicationUserDTO"/> containing the details of the specified user.
        /// </returns>
        Task<PCPApplicationUserDTO> GetUserByIdAsync(Guid id);

        /// <summary>
        /// Block or unblock a user.
        /// </summary>
        /// <param name="id">The unique identifier of the user.</param>
        /// <param name="request">The request to block or unblock the user.</param>
        /// <returns>A value indicating whether the user was blocked or unblocked.</returns>
        Task<bool> BlockUnblockUserAsync(Guid id, BlockUserDTO request);

        /// <summary>
        /// Adds a user to a list of masterclients and optionally removes the user from not mentioned masterclients.
        /// </summary>
        /// <param name="userMasterClientsDTO">The DTO with the parameters for the update.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task SetUserMasterClientsAsync(UserMasterClientsDTO userMasterClientsDTO);

        /// <summary>
        /// Gets a paged list of users with optionally additional details.
        /// </summary>
        /// <param name="request">The request with parameters for the list.</param>
        /// <returns>A <see cref="Task{IPagedList}"/> representing the asynchronous operation holding the paged list of ListUserDTO.</returns>
        Task<IPagedList<ListMasterClientUserDTO>> ListMasterClientUsersAsync(ListUsersRequest request);

        /// <summary>
        /// Gets a paged list of users with optionally additional details for multiple masterclients.
        /// </summary>
        /// <param name="request">The request with parameters for the list.</param>
        /// <returns>A <see cref="Task{IPagedList}"/> representing the asynchronous operation holding the paged list of ListUserDTO.</returns>
        Task<IPagedList<(Guid MasterClientId, List<ListUserDTO> MasterClientUserItems)>> ListMasterClientUsersByMasterClientAsync(ListUsersByMasterClientRequest request);

        /// <summary>
        /// Gets the user by email that represents a client user.
        /// </summary>
        /// <param name="emailAddress">The email address of the user.</param>
        /// <returns>A <see cref="Task{ApplicationUser}"/> representing the asynchronous operation holding the user.</returns>
        Task<ApplicationUser> GetClientUserByEmailAsync(string emailAddress);
    }
}
