// <copyright file="MongoDbFactory.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Options;
using MongoDB.Driver;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Shared.Jurisdictions;
using System.Collections.Concurrent;

namespace NetProGroup.Trust.DataMigration.Factories
{
    /// <summary>
    /// Factory for creating MongoDB clients and databases based on region.
    /// </summary>
    public class MongoDbFactory : IMongoDbFactory
    {
        private readonly DataMigrationAppSettings _appSettings;
        private readonly ConcurrentDictionary<string, IMongoClient> _clients = new();
        private readonly ConcurrentDictionary<string, IMongoDatabase> _databases = new();

        /// <summary>
        /// Initializes a new instance of the <see cref="MongoDbFactory"/> class.
        /// </summary>
        /// <param name="appSettings">The data migration application settings.</param>
        public MongoDbFactory(IOptions<DataMigrationAppSettings> appSettings)
        {
            _appSettings = appSettings?.Value ?? throw new ArgumentNullException(nameof(appSettings));
        }

        /// <inheritdoc />
        public IMongoClient GetMongoClient(string region)
        {
            ArgumentException.ThrowIfNullOrEmpty(region);

            return _clients.GetOrAdd(region, CreateMongoClient);
        }

        /// <inheritdoc />
        public IMongoDatabase GetMongoDatabase(string region)
        {
            ArgumentException.ThrowIfNullOrEmpty(region);

            return _databases.GetOrAdd(region, CreateMongoDatabase);
        }

        private IMongoClient CreateMongoClient(string region)
        {
            var jurisdictionCode = GetJurisdictionCodeFromRegion(region);
            var jurisdictionSettings = GetJurisdictionSettings(jurisdictionCode);
            
            var connectionString = jurisdictionSettings.MongoConnectionString;
            return new MongoClient(connectionString);
        }

        private IMongoDatabase CreateMongoDatabase(string region)
        {
            var client = GetMongoClient(region);
            var jurisdictionCode = GetJurisdictionCodeFromRegion(region);
            var jurisdictionSettings = GetJurisdictionSettings(jurisdictionCode);
            
            var databaseName = jurisdictionSettings.MongoDatabaseName;
            
            // Check if database exists
            if (!client.ListDatabaseNames().ToList().Contains(databaseName))
            {
                throw new APIException(
                    ApplicationErrors.DATABASE_DOES_NOT_EXIST.ToErrorCode(), 
                    $"Database {databaseName} does not exist for region {region}.");
            }
            
            return client.GetDatabase(databaseName);
        }

        private string GetJurisdictionCodeFromRegion(string region)
        {
            // Map region names to jurisdiction codes
            return region switch
            {
                "Nevis" => JurisdictionCodes.Nevis,
                "Bahamas" => JurisdictionCodes.Bahamas,
                _ => throw new ArgumentException($"Unknown region: {region}", nameof(region))
            };
        }

        private JurisdictionSettings GetJurisdictionSettings(string jurisdictionCode)
        {
            if (!_appSettings.JurisdictionSettings.TryGetValue(jurisdictionCode, out var settings))
            {
                throw new ArgumentException($"No jurisdiction settings found for jurisdiction code: {jurisdictionCode}");
            }

            return settings;
        }
    }
}
