﻿// <copyright file="UsersController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Users;
using Swashbuckle.AspNetCore.Annotations;
using X.PagedList;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.Users
{
    /// <summary>
    /// Use this controller to get a user after the user has signed in.
    /// </summary>
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/[controller]")]
    public class UsersController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;

        private readonly IUsersAppService _usersAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="UsersController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="usersAppService">The service for users.</param>
        public UsersController(
            ILogger<UsersController> logger,
            IConfiguration configuration,
            IUsersAppService usersAppService)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;

            _usersAppService = usersAppService;
        }

        /// <summary>
        /// Gets a paginated list of users.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/users
        ///
        /// </remarks>
        /// <param name="request">The request parameters for pagination and filtering.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="IPagedList"/>.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "Management_GetUsers")]
        [ProducesResponseType(typeof(PaginatedResponse<ListApplicationUsersDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetUsers([FromQuery] UsersRequestDTO request)
        {
            var result = await ProcessRequestWithPagedResponseAsync(new PagingInfo(request.PageNumber, request.PageSize),
                validate: () =>
                {
                    Check.Positive(request.PageNumber, nameof(request.PageNumber));
                    Check.Positive(request.PageSize, nameof(request.PageSize));
                },
                executeAsync: async _ => await _usersAppService.ListUsersAsync(request));
            return result.AsResponse();
        }

        /// <summary>
        /// Retrieves the details of a user by their unique identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the user.</param>
        /// <returns>
        /// An <see cref="IActionResult"/> containing the <see cref="ApplicationUserDTO"/> for the specified user.
        /// </returns>
        /// <response code="200">Returns the details of the requested user as <see cref="ApplicationUserDTO"/>.</response>
        /// <response code="401">Unauthorized access due to missing or invalid authentication.</response>
        /// <response code="404">User not found with the specified identifier.</response>
        /// <example>
        /// Example request:
        /// GET /api/management/users/{id}
        ///
        /// Example response:
        /// {
        ///   "name": "Monica",
        ///   "surname": "Doe",
        ///   "username": "<EMAIL>",
        ///   "displayName": "Monica Doe",
        ///   "email": "<EMAIL>",
        ///   "roleNames": ["Admin", "User"],
        ///   "roleIds": ["2d4b679e-3f48-42f6-929f-3a24b7e0f344", "5abfae2f-85e1-451b-9248-cd2e120e74ef"],
        ///   "isActive": true,
        ///   "applicationUserRoles": [
        ///     {
        ///       "roleId": "2d4b679e-3f48-42f6-929f-3a24b7e0f344",
        ///       "roleName": "Admin"
        ///     },
        ///     {
        ///       "roleId": "5abfae2f-85e1-451b-9248-cd2e120e74ef",
        ///       "roleName": "User"
        ///     }
        ///   ],
        ///   "objectId": null,
        ///   "id": "7a099d00-7e13-4d59-8b89-c00f551ec669"
        /// }
        /// </example>
        [HttpGet("{id:guid}")]
        [SwaggerOperation(OperationId = "Management_GetUser")]
        [ProducesResponseType(typeof(PCPApplicationUserDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> GetUser(Guid id)
        {
            PCPApplicationUserDTO item = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                },
                executeAsync: async () =>
                {
                    item = await _usersAppService.GetUserByIdAsync(id);
                },
                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Blocks or unblocks a user.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     Patch /api/v1/management/users/{userId}/block
        ///     {
        ///         "isBlocked": true
        ///     }.
        /// </remarks>
        /// <param name="userId">The ID of the user to be blocked or unblocked.</param>
        /// <param name="blockUserModel">The model containing the block/unblock status.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpPatch("{userId:guid}/block")]
        [SwaggerOperation(OperationId = "BlockUnblockUser")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> BlockUnblockUser(Guid userId, BlockUserDTO blockUserModel)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
                    Check.NotNull(blockUserModel, nameof(blockUserModel));
                },
                executeAsync: async () =>
                {
                    await _usersAppService.BlockUnblockUserAsync(userId, blockUserModel);
                });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Puts the list of masterclients for the user.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT  /api/v1/management/users/{userId}/masterclients
        ///     {
        ///         
        ///     }
        /// 
        /// </remarks>
        /// <param name="userId">The id of the user.</param>
        /// <param name="userMasterClients">The model with the masterclients to set.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpPut("{userId}/masterclients")]
        [SwaggerOperation(OperationId = "SetUserMasterClients")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> SetUserMasterClients(
            Guid userId,
            UserMasterClientsDTO userMasterClients)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotNull(userMasterClients, nameof(userMasterClients));
                },
                executeAsync: async () =>
                {
                    userMasterClients.UserId = userId;
                    await _usersAppService.SetUserMasterClientsAsync(userMasterClients);
                });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Posts a new invitation for the user.
        /// </summary>
        /// <remarks>
        /// You can pass amasterclientid to have a specific masterclientcode mentioned on the invitation.
        /// 
        /// Sample request:
        ///
        ///     POST  /api/v1/management/users/{userId}/invitation?masterclientid=
        ///     {
        ///
        ///     }.
        ///
        /// </remarks>
        /// <param name="userId">The id of the user to send the invitation to.</param>
        /// <param name="masterClientId">The optional id of the masterclient to use for the code on the invitation.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpPost("{userId}/invitation")]
        [SwaggerOperation(OperationId = "SendUserInvitation")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> SendUserInvitation(
            Guid userId,
            Guid? masterClientId)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
                },
                executeAsync: async () =>
                {
                    await _usersAppService.SendInvitationAsync(userId, masterClientId);
                });

            return result.AsNoContentResponse();
        }

        /// <summary>
        /// Posts new invitations for the list of users (and optional mastercclients).
        /// </summary>
        /// <remarks>
        /// Users in UserIds will get an invitation on the first masterclient if multiple.\
        /// Users in UserMasterClients will get an invitation on the specified masterclient.\
        /// \
        /// Sample request:\
        ///     POST  /api/v1/management/users/invitations\
        ///     {\
        ///         "UserIds": [\
        ///             "d290f1ee-6c54-4b01-90e6-d701748f0851",\
        ///             "c56a4180-65aa-42ec-a945-5fd21dec0538"\
        ///          ],\
        ///         "UserMasterClients": [\
        ///             {\
        ///                 "UserId": "d290f1ee-6c54-4b01-90e6-d701748f0851",\
        ///                 "MasterClientId": "5abfae2f-85e1-451b-9248-cd2e120e74ef"\
        ///             },\
        ///             {\
        ///                 "UserId": "c56a4180-65aa-42ec-a945-5fd21dec0538",\
        ///                 "MasterClientId": "5abfae2f-85e1-451b-9248-cd2e120e74ef"\
        ///             }\
        ///          ]\
        ///     }.
        /// </remarks>
        /// <param name="request">The DTO with the ids of the users.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpPost("invitations")]
        [SwaggerOperation(OperationId = "SendUserInvitations")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> SendUserInvitations(
                SendInvitationsDTO request)
        {
            var result = await ProcessRequestAsync<object>(
                validate: () => { },
                executeAsync: async () =>
                {
                    await _usersAppService.SendInvitationsAsync(request);
                });

            return result.AsNoContentResponse();
        }

#if DEBUG
        /// <summary>
        /// Posts a new user.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     PUT  /api/v1/management/users
        ///     {
        ///     }
        /// .
        /// </remarks>
        /// <param name="createUserModel">The model of the user to create.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the user info.</returns>
        [HttpPost]
        [SwaggerOperation(OperationId = "CreateUser")]
        [ProducesResponseType(typeof(ApplicationUserDTO), StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> Create(CreateUserDTO createUserModel)
        {
            ApplicationUserDTO item = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                    Check.NotNull(createUserModel, nameof(createUserModel));
                },
                executeAsync: async () =>
                {
                    item = await _usersAppService.CreateUserAsync(createUserModel);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }
#endif
    }
}
