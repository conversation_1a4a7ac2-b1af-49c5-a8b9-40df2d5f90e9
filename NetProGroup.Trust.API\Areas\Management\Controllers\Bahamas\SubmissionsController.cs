// <copyright file="SubmissionsController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.Application.Contracts.Submissions;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.Bahamas
{
    /// <summary>
    /// Use this controller for Bahamas related events.
    /// </summary>
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/bahamas/[controller]")]
    public class SubmissionsController : TrustAPIControllerBase
    {
        private readonly ISubmissionsAppService _submissionsAppService;
        private readonly ISubmissionReportsAppService _submissionReportsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="SubmissionsController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="submissionsAppService">The service for submissions.</param>
        /// <param name="submissionReportsAppService">The service for submissions reports.</param>
        public SubmissionsController(
            ILogger<SubmissionsController> logger,
            ISubmissionsAppService submissionsAppService,
            ISubmissionReportsAppService submissionReportsAppService)
            : base(logger)
        {
            _submissionsAppService = submissionsAppService;
            _submissionReportsAppService = submissionReportsAppService;
        }

        /// <summary>
        /// Gets the list of submissions that match the criteria.
        /// </summary>
        /// <remarks>
        /// Use the GeneralSearchTerm to search in either entity name, masterclient code or referral office.
        ///
        /// Sample request:
        ///
        ///     GET /api/v1/management/bahamas/submissions.
        /// </remarks>
        /// <param name="request">Request model holding paging and searching parameters.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the submissions.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "Management_Bahamas_ListSubmissionsByModule")]
        [ProducesResponseType(typeof(PaginatedResponse<ListSubmissionBahamasDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> FilterSubmissionsForBahamas(
            [FromQuery] FilterSubmissionsRequestForBahamasDTO request)
        {
            var result = await ProcessRequestWithPagedResponseAsync(
                validate: () =>
                    {
                        Check.NotNull(request, nameof(request));
                        Check.NotDefaultOrNull<Guid>(request.ModuleId, nameof(request.ModuleId));
                    },

                    executeAsync: async _ =>
                    {
                        return await _submissionsAppService.SearchSubmissionsForBahamasAsync(request);
                    });

            return result.AsResponse();
        }

        /// <summary>
        /// Generate a submission report given a module and a filter request.
        /// </summary>
        /// <remarks>
        ///
        /// Sample request:
        ///
        ///     GET /api/v1/management/bahamas/submissions/report.
        /// </remarks>
        /// <param name="request">Request model holding filter and searching parameters.</param>
        /// <returns>
        /// A <see cref="FileContentResult"/> containing the economic subsantance report file with appropriate content type
        /// and file name if the download is successful. Returns an appropriate response code if the request is unauthorized or forbidden.
        /// </returns>
        [HttpGet("report")]
        [SwaggerOperation(OperationId = "Management_Bahamas_GenerateSubmissionsReport")]
        [ProducesResponseType(typeof(FileContentResult), StatusCodes.Status200OK)]
        public async Task<IActionResult> GenerateSubmissionsReport(
            [FromQuery] SubmissionsReportRequestBahamasDTO request)
        {
            ReportDownloadResponseDTO item = null;
            var result = await ProcessRequestAsync<ExportSubmissionResponseDTO>(
                validate: () =>
                {
                    Check.NotNull(request, nameof(request));
                    Check.NotNull(request.ModuleId, nameof(request.ModuleId));
                    ValidateWorkContextUserId();
                },
                executeAsync: async () =>
                {
                    item = await _submissionReportsAppService.GenerateSubmissionsReportForBahamasAsync(request);
                });

            return !result.IsValid ? result.AsResponse() :
                File(item.FileContent.ToArray(), item.ContentType, item.FileName);
        }
    }
}