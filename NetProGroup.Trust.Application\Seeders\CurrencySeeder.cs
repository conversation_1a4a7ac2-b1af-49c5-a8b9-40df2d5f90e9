﻿using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Currencies;

namespace NetProGroup.Trust.Application.Seeders
{
    /// <summary>
    /// Seeder for the currencies.
    /// </summary>
    public static partial class CurrencySeeder
    {
        /// <summary>
        /// Defines the seeding.
        /// </summary>
        /// <param name="modelBuilder">Instance of ModelBuilder.</param>
        public static void SeedCurrencies(this ModelBuilder modelBuilder)
        {
            ArgumentNullException.ThrowIfNull(modelBuilder);

            var date = new DateTime(2024, 1, 1);

            modelBuilder.Entity<Currency>().HasData(
                new Currency(new Guid("6ca5809f-06a3-4e99-9e0a-df570d3a482f"), "United States Dollar", "USD", "$")
                {
                    CreatedAt = date,
                    UpdatedAt = date
                },
                new Currency(new Guid("a8ef9d12-0e2c-4231-8cb0-98f78513a8c3"), "Euro", "EUR", "€")
                {
                    CreatedAt = date,
                    UpdatedAt = date
                },
                new Currency(new Guid("b8c2e7a4-66c7-488c-9d02-6a4199b3e199"), "British Pound", "GBP", "£")
                {
                    CreatedAt = date,
                    UpdatedAt = date
                },
                new Currency(new Guid("3f8f100d-5707-4756-9f0e-3541d07ac497"), "Japanese Yen", "JPY", "¥")
                {
                    CreatedAt = date,
                    UpdatedAt = date
                },
                new Currency(new Guid("bd4826fb-5562-472e-bd5c-8f0a3ffae49a"), "Swiss Franc", "CHF", "CHF")
                {
                    CreatedAt = date,
                    UpdatedAt = date
                },
                new Currency(new Guid("9e54f888-4d24-4c1d-9d0d-6b8b1d0b92ef"), "Canadian Dollar", "CAD", "$")
                {
                    CreatedAt = date,
                    UpdatedAt = date
                },
                new Currency(new Guid("c3b4b7f4-11b5-4b9f-9f66-1701b82b2e69"), "Australian Dollar", "AUD", "$")
                {
                    CreatedAt = date,
                    UpdatedAt = date
                },
                new Currency(new Guid("7b4e8c6e-5a7b-47a7-99ea-22d8b415d5f7"), "Chinese Yuan", "CNY", "¥")
                {
                    CreatedAt = date,
                    UpdatedAt = date
                }
            );
        }
    }
}
