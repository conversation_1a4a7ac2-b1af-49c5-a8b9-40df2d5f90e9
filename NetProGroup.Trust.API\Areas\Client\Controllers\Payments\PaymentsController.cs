using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Payments;
using NetProGroup.Trust.Application.Contracts.Payments.Request;
using NetProGroup.Trust.Application.Contracts.Payments.Response;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Client.Controllers.Payments
{
    /// <summary>
    /// Use this controller for payments related methods.
    /// </summary>
    [Area("Client")]
    [Route("api/v{version:apiVersion}/[area]/payments")]
    public class PaymentsController : TrustAPIControllerBase
    {
        private readonly IPaymentsAppService _paymentsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="PaymentsController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="paymentsAppService">An instance of paymentsAppService</param>
        public PaymentsController(
            ILogger<PaymentsController> logger,
            IConfiguration configuration,
            IPaymentsAppService paymentsAppService)
            : base(logger)
        {
            _paymentsAppService = paymentsAppService;
        }

        /// <summary>
        /// Gets the given payments.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/client/payments/
        ///
        /// </remarks>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the resulting <see cref="IPagedList<PaymentDTO>"/>.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "Client_GetPayments")]
        [ProducesResponseType(typeof(PaginatedResponse<PaymentDTO>), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPayments([FromQuery] PaymentsRequestDTO request)
        {
            var result = await ProcessRequestWithPagedResponseAsync(
                new PagingInfo(request.PageNumber, request.PageSize),
                validate: () => { },
                executeAsync: async (pagingInfo) =>
                {
                    return await _paymentsAppService.ListPaymentsAsync(request);
                });
            return result.AsResponse();
        }

        /// <summary>
        /// Retrieves the details of a payment by its unique identifier.
        /// </summary>
        /// <param name="id">The unique identifier of the payment.</param>
        /// <returns>
        /// An <see cref="IActionResult"/> containing the <see cref="PaymentDetailsResponseDTO"/> for the specified payment.
        /// </returns>
        /// <response code="200">Returns the details of the requested payment as <see cref="PaymentDetailsResponseDTO"/>.</response>
        /// <response code="401">Unauthorized access due to missing or invalid authentication.</response>
        /// <response code="403">Access to the requested payment is forbidden.</response>
        /// <example>
        /// Example request:
        /// GET /api/clients/{id}
        ///
        /// Example response:
        /// {
        ///   "id": "a1b2c3d4-e5f6-7890-ab12-3456def789ab",
        ///   "legalEntityId": "d3f6a789-0123-4567-890a-bcdef0123456",
        ///   "currencyId": "7890ab12-3456-def7-8901-234567890abc",
        ///   "amount": 100.50,
        ///   "status": 1, // "Completed"
        ///   "paymentTransactions": [
        ///     {
        ///       "result": "Success",
        ///       "resultCode": "00",
        ///       "resultMessage": "Transaction completed successfully.",
        ///       "transactionId": "tx123456789",
        ///       "status": "Completed",
        ///       "processCreatedAt": "2023-05-15T10:20:30Z",
        ///       "paidAt": "2023-05-15T10:21:00Z",
        ///       "isFinished": true,
        ///       "paymentProviderId": "4567890a-bcde-f012-3456-789abcde0123"
        ///     },
        ///     {
        ///       "result": "Failure",
        ///       "resultCode": "01",
        ///       "resultMessage": "Transaction failed due to insufficient funds.",
        ///       "transactionId": "tx987654321",
        ///       "status": "Failed",
        ///       "processCreatedAt": "2023-05-16T11:22:30Z",
        ///       "paidAt": null,
        ///       "isFinished": true,
        ///       "paymentProviderId": "4567890a-bcde-f012-3456-789abcde0123"
        ///     }
        ///   ]
        /// }
        /// </example>
        [HttpGet("{id:guid}")]
        [SwaggerOperation(OperationId = "Client_GetPayment")]
        [ProducesResponseType(typeof(PaymentDetailsResponseDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetPayment(Guid id)
        {
            PaymentDetailsResponseDTO item = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _paymentsAppService.GetPaymentAsync(id);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }


        /// <summary>
        /// Creates a new payment transaction based on the provided request data.
        /// </summary>
        /// <param name="createPaymentRequestDto">The request object containing payment details such as legal entity ID, currency ID, and associated invoice IDs.</param>
        /// <returns>
        /// A task representing the asynchronous operation.
        /// On success, returns a <see cref="PaymentDTO"/> object with the created payment details and a 201 status code.
        /// </returns>
        /// <response code="201">Payment created successfully, returns the payment details in the response body.</response>
        /// <response code="400">Invalid request, the input parameters did not pass validation.</response>
        /// <response code="401">Unauthorized, authentication is required to access this resource.</response>
        /// <response code="403">Forbidden, the user does not have permission to create the payment.</response>
        /// <example>
        /// Example request body:
        /// {
        ///   "legalEntityId": "b4f2b7a4-7c0b-47e3-9a6a-935f5eae9f42",
        ///   "currencyId": "3f8d5cba-3df4-49a7-a1ab-42c4e55f9b5c",
        ///   "invoiceIds": [
        ///     "8d2a179e-5d75-4f16-ae0e-b95f5c9f0ed6",
        ///     "7d2b2a1e-5c65-4a26-bd3d-e75a6c7f2d47"
        ///   ]
        /// }
        /// </example>
        [HttpPost]
        [SwaggerOperation(OperationId = "Client_CreatePayment")]
        [ProducesResponseType(typeof(PaymentDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> CreatePayment([FromBody] CreatePaymentRequestDTO createPaymentRequestDto)
        {
            CreatePaymentResponseDTO createdTransaction = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                    Check.NotNull(createPaymentRequestDto, nameof(createPaymentRequestDto));

                    // Add other validation checks here as needed
                },
                executeAsync: async () =>
                {
                    createdTransaction = await _paymentsAppService.AddPaymentAsync(createPaymentRequestDto);
                },
                createResponseModel: () =>
                {
                    return createdTransaction;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Cancels a payment by its unique identifier.
        /// </summary>
        /// <param name="paymentId">The unique identifier of the payment.</param>
        /// <example>
        /// Example request:
        ///
        /// PUT /api/v1/client/payments/{paymentId}/cancelled.
        ///
        /// </example>
        /// <returns>A <see cref="Task{IActionResult}"/> representing the asynchronous operation.</returns>
        [HttpPut("{paymentId}/cancelled")]
        [SwaggerOperation(OperationId = "Client_CancelPayment")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<IActionResult> CancelPayment(Guid paymentId)
        {
            CreatePaymentResponseDTO createdTransaction = null;

            var result = await ProcessRequestAsync(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(paymentId, nameof(paymentId));
                },
                executeAsync: async () =>
                {
                    await _paymentsAppService.CancelPaymentAsync(paymentId);
                },
                createResponseModel: () =>
                {
                    return createdTransaction;
                });

            return result.AsNoContentResponse();
        }
    }
}