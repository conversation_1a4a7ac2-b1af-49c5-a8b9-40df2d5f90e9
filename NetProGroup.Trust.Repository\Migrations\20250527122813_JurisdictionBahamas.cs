﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace NetProGroup.Trust.Domain.Repository.Migrations
{
    /// <inheritdoc />
    public partial class JurisdictionBahamas : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // This was mistakenly already done with code in the seeder, so converted it to SQL upsert so it works on PRD and on local machines with fresh databases.
            migrationBuilder.Sql(@"
            MERGE INTO Jurisdictions AS Target
            USING (VALUES
                ('2ca76629-b3e6-409d-b788-c63c802a4d4f', 'Bahamas', NULL, '2024-01-01T00:00:00', 0, 'Bahamas', '2024-01-01T00:00:00'),
                ('298175e2-a24f-4a7e-b7f3-a83e0447908c', 'Panama', NULL, '2024-01-01T00:00:00', 0, 'Panama', '2024-01-01T00:00:00'),
                ('ab54f8a6-dc29-4dd2-a39c-660bb980f789', 'BVI', NULL, '2024-01-01T00:00:00', 0, 'BVI', '2024-01-01T00:00:00')
            ) AS Source (Id, Code, ConcurrencyStamp, CreatedAt, InitialSyncCompleted, Name, UpdatedAt)
            ON Target.Id = Source.Id
            WHEN MATCHED THEN
                UPDATE SET
                    Code = Source.Code,
                    ConcurrencyStamp = Source.ConcurrencyStamp,
                    CreatedAt = Source.CreatedAt,
                    InitialSyncCompleted = Source.InitialSyncCompleted,
                    Name = Source.Name,
                    UpdatedAt = Source.UpdatedAt
            WHEN NOT MATCHED BY TARGET THEN
                INSERT (Id, Code, ConcurrencyStamp, CreatedAt, InitialSyncCompleted, Name, UpdatedAt)
                VALUES (Source.Id, Source.Code, Source.ConcurrencyStamp, Source.CreatedAt, Source.InitialSyncCompleted, Source.Name, Source.UpdatedAt);
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DeleteData(
                table: "Jurisdictions",
                keyColumn: "Id",
                keyValue: new Guid("2ca76629-b3e6-409d-b788-c63c802a4d4f"));
            migrationBuilder.DeleteData(
                table: "Jurisdictions",
                keyColumn: "Id",
                keyValue: new Guid("298175e2-a24f-4a7e-b7f3-a83e0447908c"));

            migrationBuilder.DeleteData(
                table: "Jurisdictions",
                keyColumn: "Id",
                keyValue: new Guid("ab54f8a6-dc29-4dd2-a39c-660bb980f789"));
        }
    }
}
