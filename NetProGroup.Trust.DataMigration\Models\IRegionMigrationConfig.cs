using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.DataMigration.Models;

public interface IRegionMigrationConfig : IScopedService
{
    IEnumerable<EntityMigrationStep> GetMigrationSteps();
    string Region { get; }
}

public class EntityConfiguration(Type entityType, string collectionName, string displayName)
{
    public Type EntityType { get; } = entityType;
    public string CollectionName { get; } = collectionName;
    public string DisplayName { get; } = displayName;
}