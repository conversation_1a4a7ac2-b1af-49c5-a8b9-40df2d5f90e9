﻿using NetProGroup.Framework.EF.Repository;

namespace NetProGroup.Trust.Application.Contracts.Users
{
    /// <summary>
    /// DTO for displaying a list of application users.
    /// </summary>
    public class ListApplicationUsersDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Gets or sets the email address of the user.
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the user is blocked.
        /// </summary>
        public virtual bool IsBlocked { get; set; }

        /// <summary>
        /// Gets or sets the role in the portal of the user (Client or Management).
        /// </summary>
        public string PrimaryRoleLabel { get; set; }
    }
}