﻿// <copyright file="ListSubmissionBahamasDTO.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.Application.Contracts.Forms;
using NetProGroup.Trust.DomainShared.Enums;

namespace NetProGroup.Trust.Application.Contracts.Submissions
{
    /// <summary>
    /// Represents a submission.
    /// </summary>
    public class ListSubmissionBahamasDTO : EntityDTO<Guid>
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ListSubmissionBahamasDTO"/> class.
        /// </summary>
        public ListSubmissionBahamasDTO()
            : base()
        {
        }

        /// <summary>
        /// Gets or sets the name of the submission.
        /// </summary>
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the email that submitted this submission.
        /// </summary>
        public string CreatedByEmail { get; set; }

        /// <summary>
        /// Gets or sets the code of the legal entity.
        /// </summary>
        public string LegalEntityCode { get; set; }

        /// <summary>
        /// Gets or sets the name of the legal entity.
        /// </summary>
        public string LegalEntityName { get; set; }

        /// <summary>
        /// Gets or sets the code of the master client.
        /// </summary>
        public string MasterClientCode { get; set; }

        /// <summary>
        /// Gets or sets the ViewPoint code of the legal entity.
        /// </summary>
        public string LegalEntityVPCode { get; set; }

        /// <summary>
        /// Gets or sets the overall status of the submission.
        /// </summary>
        public SubmissionStatus Status { get; set; }

        /// <summary>
        /// Gets or sets the date and time that the submission was created.
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the CreatedAt datetime as local time according to the jurisdiction timezone.
        /// </summary>
        public DateTime? CreatedAtLocal { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the submission was finalized.
        /// </summary>
        /// <value>
        /// A nullable <see cref="DateTime"/> representing the finalization timestamp of the submission.
        /// If the submission is not finalized, the value will be <c>null</c>.
        /// </value>
        public DateTime? SubmittedAt { get; set; }

        /// <summary>
        /// Gets or sets the SubmittedAt datetime as local time according to the jurisdiction timezone.
        /// </summary>
        public DateTime? SubmittedAtLocal { get; set; }

        /// <summary>
        /// Gets or sets the date that the submission was reopened.
        /// </summary>
        public DateTime? ReopenedAt { get; set; }

        /// <summary>
        /// Gets or sets the date that the submission was initially submitted.
        /// </summary>
        public DateTime? InitialSubmittedAt { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the submission was exported.
        /// </summary>
        /// <value>
        /// A nullable <see cref="DateTime"/> representing the export timestamp of the submission.
        /// If the submission is not exported, the value will be <c>null</c>.
        /// </value>
        public DateTime? ExportedAt { get; set; }

        /// <summary>
        /// Gets or sets the Exported datetime as local time according to the jurisdiction timezone.
        /// </summary>
        public DateTime? ExportedAtLocal { get; set; }

        /// <summary>
        /// Gets or sets the Legal entity incorporation date.
        /// </summary>
        public DateTime? IncorporationDate { get; set; }

        /// <summary>
        /// Gets or sets the incorporation code of the company.
        /// </summary>
        public string IncorporationCode { get; set; }

        /// <summary>
        /// Gets or sets the used payment method.
        /// </summary>
        public string PaymentMethod { get; set; }

        /// <summary>
        /// Gets or sets the payment reference.
        /// </summary>
        public string PaymentReference { get; set; }

        /// <summary>
        /// Gets or sets the date/time that the payment is received.
        /// </summary>
        public DateTime? PaymentReceivedAt { get; set; }

        /// <summary>
        /// Gets or sets the PaymentReceivedAt datetime as local time according to the jurisdiction timezone.
        /// </summary>
        public DateTime? PaymentReceivedAtLocal { get; set; }

        /// <summary>
        /// Gets or sets the start of the financial period.
        /// </summary>
        public DateTime? FinancialPeriodStartsAt { get; set; }

        /// <summary>
        /// Gets or sets the end of the financial period.
        /// </summary>
        public DateTime? FinancialPeriodEndsAt { get; set; }

        /// <summary>
        /// Gets or sets the referral office.
        /// </summary>
        public string LegalEntityReferralOffice { get; set; }

        /// <summary>
        /// Gets or sets the reopen request comments.
        /// </summary>
        public string ReopenRequestComments { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the submission has the relevant activity None selected.
        /// </summary>
        public bool HasActivityNone { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the submission has the relevant activity HoldingBusiness selected.
        /// </summary>
        public bool HasActivityHoldingBusiness { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the submission has the relevant activity FinanceLeasingBusiness selected.
        /// </summary>
        public bool HasActivityFinanceLeasingBusiness { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the submission has the relevant activity BankingBusiness selected.
        /// </summary>
        public bool HasActivityBankingBusiness { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the submission has the relevant activity InsuranceBusiness selected.
        /// </summary>
        public bool HasActivityInsuranceBusiness { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the submission has the relevant activity FundManagementBusiness selected.
        /// </summary>
        public bool HasActivityFundManagementBusiness { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the submission has the relevant activity HeadquartersBusiness selected.
        /// </summary>
        public bool HasActivityHeadquartersBusiness { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the submission has the relevant activity ShippingBusiness selected.
        /// </summary>
        public bool HasActivityShippingBusiness { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the submission has the relevant activity IntellectualPropertyBusiness selected.
        /// </summary>
        public bool HasActivityIntellectualPropertyBusiness { get; set; }

        /// <summary>
        /// Gets or sets a value indicating the request information Id.
        /// </summary>
        public string RequestForinformation { get; set; }

        /// <summary>
        /// Gets or sets a value indicating the request information status.
        /// </summary>
        public string RequestForInformationStatus { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the request for information was completed.
        /// </summary>
        public DateTime? RequestForInformationCompletedAt { get; set; }

        /// <summary>
        /// Gets or sets an indication whether the submission must be paid or unpaid.
        /// </summary>
        public bool? IsPaid { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the entity is marked as deleted.
        /// When true, the entity is considered soft deleted.
        /// </summary>
        public bool IsDeleted { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the entity was marked as deleted.
        /// This property is null if the entity has not been deleted.
        /// </summary>
        public DateTime? DeletedAt { get; set; }
    }
}