using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Nevis
{
    /// <summary>
    /// Represents a financial report in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class FinancialReport
    {
        /// <summary>
        /// Gets or sets a value indicating whether assessable income has been generated.
        /// </summary>
        [BsonElement("have_income_generated")]
        public bool? HaveIncomeGenerated { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether there are activity conditions met.
        /// </summary>
        [BsonElement("activity_conditions")]
        public bool? ActivityConditions { get; set; }

        /// <summary>
        /// Gets or sets the list of financial activities reported.
        /// </summary>
        [BsonElement("activities_reported")]
        public List<FinancialActivityReport> ActivitiesReported { get; set; }
    }

    /// <summary>
    /// Represents a financial activity report in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class FinancialActivityReport
    {
        /// <summary>
        /// Gets or sets the description of the financial activity.
        /// </summary>
        [BsonElement("description")]
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the activity is related to a related party intellectual property.
        /// </summary>
        [BsonElement("related_party")]
        public bool? RelatedParty { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the activity is related to a non-related party intellectual property.
        /// </summary>
        [BsonElement("non_related_intellectual")]
        public bool? NonRelatedIntellectual { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the activity is related to non-intellectual property.
        /// </summary>
        [BsonElement("non_intellectual_property")]
        public bool? NonIntellectualProperty { get; set; }

        /// <summary>
        /// Gets or sets the year and amount of income. Consider changing this to a more appropriate type if possible.
        /// </summary>
        [BsonElement("year_amount_income")]
        public string YearAmountIncome { get; set; }

        /// <summary>
        /// Gets or sets the amount of income. Consider changing this to a decimal or double if possible.
        /// </summary>
        [BsonElement("amount_income")]
        public string AmountIncome { get; set; }
    }
}
