using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Nevis
{
    /// <summary>
    /// Represents intellectual property acquired information in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class IntellectualPropertyAcquired
    {
        /// <summary>
        /// Gets or sets a value indicating whether intellectual property has been acquired.
        /// </summary>
        [BsonElement("is_acquired_intellectual_property")]
        public bool? IsAcquiredIntellectualProperty { get; set; }

        /// <summary>
        /// Gets or sets the list of intellectual property assets acquired.
        /// </summary>
        [BsonElement("assets_acquired")]
        public List<AssetsAcquired> AssetsAcquired { get; set; }
    }

    /// <summary>
    /// Represents an intellectual property asset acquired in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class AssetsAcquired
    {
        /// <summary>
        /// Gets or sets the description of the intellectual property asset acquired.
        /// </summary>
        [BsonElement("description")]
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the start date of the intellectual property asset acquisition.
        /// </summary>
        [BsonElement("start_date")]
        public DateTime StartDate { get; set; }

        /// <summary>
        /// Gets or sets the income generated from the intellectual property asset.
        /// Consider changing this to a decimal or double if possible.
        /// </summary>
        [BsonElement("income")]
        public string Income { get; set; }
    }
}
