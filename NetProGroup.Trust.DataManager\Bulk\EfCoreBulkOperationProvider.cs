// <copyright file="EfCoreBulkOperationProvider.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using EFCore.BulkExtensions;
using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.DataManager.Bulk
{
    /// <summary>
    /// Implementation of bulk operations using EFCore.BulkExtensions.
    /// </summary>
    public class EfCoreBulkOperationProvider : IBulkOperationProvider, ISingletonService
    {
        /// <inheritdoc/>
        public async Task BulkInsertAsync<T>(IEnumerable<T> entities, DbContext context, int? batchSize = null, bool? includeGraph = false) where T : class
        {
            var bulkConfig = new BulkConfig
            {
                UseTempDB = true,
                SetOutputIdentity = true
            };

            bulkConfig.BatchSize = batchSize ?? bulkConfig.BatchSize;
            bulkConfig.IncludeGraph = includeGraph ?? bulkConfig.IncludeGraph;

            await context.BulkInsertAsync(entities.ToList(), bulkConfig);
        }

        /// <inheritdoc/>
        public async Task BulkUpdateAsync<T>(IEnumerable<T> entities, DbContext context, int? batchSize = null, bool? includeGraph = false) where T : class
        {
            var bulkConfig = new BulkConfig
            {
                UseTempDB = true,
                SetOutputIdentity = true
            };

            bulkConfig.BatchSize = batchSize ?? bulkConfig.BatchSize;
            bulkConfig.IncludeGraph = includeGraph ?? bulkConfig.IncludeGraph;

            await context.BulkUpdateAsync(entities.ToList(), bulkConfig);
        }

        /// <inheritdoc/>
        public async Task BulkDeleteAsync<T>(IEnumerable<T> entities, DbContext context, int? batchSize = null) where T : class
        {
            var bulkConfig = new BulkConfig
            {
                UseTempDB = true,
                SetOutputIdentity = true
            };

            bulkConfig.BatchSize = batchSize ?? bulkConfig.BatchSize;

            await context.BulkDeleteAsync(entities.ToList(), bulkConfig);
        }

        /// <inheritdoc/>
        public async Task<int> BulkSaveChangesAsync(DbContext context)
        {
            ArgumentNullException.ThrowIfNull(context, nameof(context));

            return await context.SaveChangesAsync();
        }
    }
}
