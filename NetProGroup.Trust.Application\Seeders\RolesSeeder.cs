// <copyright file="RolesSeeder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Trust.Domain.Shared.Roles;

namespace NetProGroup.Trust.Application.Seeders
{
    /// <summary>
    /// Seeder for the roles in ProjectsDashboard.
    /// </summary>
    public static partial class RolesSeeder
    {
        /// <summary>
        /// Defines the seed.
        /// </summary>
        /// <param name="modelBuilder">The ModelBuilder entity.</param>
        public static void SeedRoles(this ModelBuilder modelBuilder)
        {
            ArgumentNullException.ThrowIfNull(modelBuilder, nameof(modelBuilder));

            modelBuilder.Entity<ApplicationRole>().HasData
            (
                new ApplicationRole()
                {
                    Id = WellKnownRoleIds.System,
                    ConcurrencyStamp = WellKnownRoleIds.System.ToString(),
                    Name = WellKnownRoleNames.System,
                    NormalizedName = WellKnownRoleNames.System.ToUpper(),
                    DisplayName = WellKnownRoleNames.System
                },
                new ApplicationRole()
                {
                    Id = Guid.Parse("A00D4E6C-DCD6-4FCA-B080-666EEB9AE6CE"),
                    ConcurrencyStamp = "A00D4E6C-DCD6-4FCA-B080-666EEB9AE6CE",
                    Name = WellKnownRoleNames.Nevis_Owner,
                    NormalizedName = WellKnownRoleNames.Nevis_Owner.ToUpper(),
                    DisplayName = WellKnownRoleNames.Nevis_Owner
                },
                new ApplicationRole()
                {
                    Id = Guid.Parse("DAAAF3B2-E632-4EC8-8418-DC012239FD73"),
                    ConcurrencyStamp = "DAAAF3B2-E632-4EC8-8418-DC012239FD73",
                    Name = WellKnownRoleNames.Common_SuperAdmin,
                    NormalizedName = WellKnownRoleNames.Common_SuperAdmin.ToUpper(),
                    DisplayName = WellKnownRoleNames.Common_SuperAdmin
                });
        }
    }
}
