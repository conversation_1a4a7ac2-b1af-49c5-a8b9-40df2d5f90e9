# ASP.NET Core (.NET Framework)
# Build and test ASP.NET Core projects targeting the full .NET Framework.
# Add steps that publish symbols, save build artifacts, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core

jobs:
- job: Build
  variables:
    solution: '**/*.sln'
    buildPlatform: 'Any CPU'
    buildConfiguration: 'Release'
    NUGET_PACKAGES: $(Pipeline.Workspace)/.nuget/packages
    isPR: $[eq(variables['Build.Reason'], 'PullRequest')]
  pool:
    vmImage: 'windows-latest'
  steps:
  # Delete nuget.config otherwise Install EF Tool will fail with 401
  - task: DeleteFiles@1
    displayName: 'Delete nuget.config'
    inputs:
      Contents: 'nuget.config'

  - task: Cache@2
    displayName: Cache Publish Output
    inputs:
      key: 'publish | "$(Agent.OS)" | **/*.*,!ci/*.yml,!**/bin/**,!**/obj/**,!README.md,!.git/**,!NetProGroup.Trust.API/App_Data/logs/**'
      path: $(Build.ArtifactStagingDirectory)
      cacheHitVar: 'PUBLISH_CACHE_RESTORED'

  - task: Cache@2
    displayName: Cache NuGet packages
    condition: ne(variables.PUBLISH_CACHE_RESTORED, 'true')
    inputs:
      key: 'nuget | "$(Agent.OS)" | **/packages.lock.json,**/*.csproj,!**/bin/**,!**/obj/**'
      path: $(NUGET_PACKAGES)
      cacheHitVar: 'CACHE_RESTORED'

  - task: DotNetCoreCLI@2
    condition: and(succeeded(), ne(variables.PUBLISH_CACHE_RESTORED, 'true'))
    displayName: Restore
    inputs:
      command: 'restore'
      projects: '$(solution)'
      feedsToUse: 'select'
      vstsFeed: 'a81bf734-ab39-4d00-a7c6-511c57483a88'
      verbosityRestore: Normal
      restoreArguments:  '/property:Configuration=$(buildConfiguration)'

  #- task: VSBuild@1
  #  inputs:
  #    solution: '$(solution)'
  #    msbuildArgs: '/p:DeployOnBuild=true /p:WebPublishMethod=Package /p:PackageAsSingleFile=true /p:SkipInvalidConfigurations=true /p:DesktopBuildPackageLocation="$(build.artifactStagingDirectory)\WebApp.zip" /p:DeployIisAppPath="Default Web Site"'
  #    platform: '$(buildPlatform)'
  #    configuration: '$(buildConfiguration)'

  - task: DotNetCoreCLI@2
    condition: and(succeeded(), ne(variables.PUBLISH_CACHE_RESTORED, 'true'))
    displayName: Build
    inputs:
      projects: '$(solution)'
      verbosityRestore: Detailed
      arguments: '--configuration $(buildConfiguration) --no-restore /p:ContinuousIntegrationBuild=true /p:Deterministic=true'

  - task: VSTest@2
    condition: and(succeeded(), ne(variables.PUBLISH_CACHE_RESTORED, 'true'))
    inputs:
      platform: '$(buildPlatform)'
      configuration: '$(buildConfiguration)'
      runInParallel: true

  - task: DotNetCoreCLI@2
    condition: and(succeeded(), ne(variables.PUBLISH_CACHE_RESTORED, 'true'))
    displayName: 'Install EF Tool'
    inputs:
      command: custom
      custom: tool
      arguments: 'install --global dotnet-ef --version 8.*'
      verbosityRestore: Detailed
      verbosityPack: Detailed

  - task: PowerShell@2
    condition: and(succeeded(), ne(variables.PUBLISH_CACHE_RESTORED, 'true'))
    displayName: 'Check for pending EF model changes'
    inputs:
      filePath: '$(Build.SourcesDirectory)/ci/check-ef-changes.ps1'
      arguments: '-BuildSourcesDirectory "$(Build.SourcesDirectory)"'

  - task: DotNetCoreCLI@2
    displayName: Publish
    condition: and(succeeded(), not(eq(variables.isPR, 'true')), ne(variables.PUBLISH_CACHE_RESTORED, 'true'))
    inputs:
      command: publish
      publishWebProjects: false
      projects: '**/NetProGroup.Trust.API.csproj'
      arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory) --no-build'
      verbosityPack: Detailed

  - task: DotNetCoreCLI@2
    displayName: 'Create SQL migration scripts'
    condition: and(succeeded(), not(eq(variables.isPR, 'true')), ne(variables.PUBLISH_CACHE_RESTORED, 'true'))
    inputs:
      command: custom
      custom: ef
      arguments: >-
        migrations script
        --output $(Build.ArtifactStagingDirectory)\SQL\migrationscript.sql
        --idempotent
        --project $(Build.SourcesDirectory)\NetProGroup.Trust.API\NetProGroup.Trust.API.csproj
        --no-build
        --configuration $(buildConfiguration)
      verbosityRestore: Detailed
      verbosityPack: Detailed
    
  - task: CopyFiles@2
    displayName: 'Copy SQL migration scripts'
    condition: and(succeeded(), not(eq(variables.isPR, 'true')), ne(variables.PUBLISH_CACHE_RESTORED, 'true'))
    inputs:
      SourceFolder: '$(Build.SourcesDirectory)\NetProGroup.Trust.Repository\SQL'
      Contents: '**'
      TargetFolder: '$(Build.ArtifactStagingDirectory)\SQL'

  - task: PublishPipelineArtifact@1
    displayName: 'Publish Pipeline Artifact'
    condition: and(succeeded(), not(eq(variables.isPR, 'true')))
    inputs:
      targetPath: '$(Build.ArtifactStagingDirectory)'
      artifact: 'netprotrust-api'
