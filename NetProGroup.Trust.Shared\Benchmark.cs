// <copyright file="Benchmark.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Diagnostics;

namespace NetProGroup.Trust.Domain.Shared
{
    /// <summary>
    /// Utility class for benchmarking code execution time.
    /// Usage:
    /// <code>
    /// using (new BenchMark("OperationName"))
    /// {
    ///     // Code to benchmark
    /// }
    /// </code>
    /// </summary>
    public class Benchmark : IDisposable
    {
        private readonly string _name;
        private readonly Stopwatch _stopwatch;
        private bool _disposed;

        /// <summary>
        /// Initializes a new instance of the <see cref="Benchmark"/> class.
        /// </summary>
        /// <param name="name">The name of the operation being benchmarked.</param>
        public Benchmark(string name)
        {
            _name = name;
            _stopwatch = new Stopwatch();

            _stopwatch.Start();
        }

        /// <summary>
        /// Disposes the benchmark and logs the elapsed time.
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        /// <summary>
        /// Disposes the benchmark and logs the elapsed time.
        /// </summary>
        /// <param name="disposing">Whether the object is being disposed.</param>
        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _stopwatch.Stop();
                    var elapsed = _stopwatch.Elapsed;
                    Console.WriteLine($"Benchmark completed in {elapsed.TotalMilliseconds:F2}ms ({elapsed.TotalSeconds:F2}s) {_name} ");
                }

                _disposed = true;
            }
        }
    }
}
