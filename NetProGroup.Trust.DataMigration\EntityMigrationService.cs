// <copyright file="EntityMigrationService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MongoDB.Driver;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using System.Reflection;
using NetProGroup.Trust.DataMigration.Models;
using NetProGroup.Trust.DataMigration.Factories;

namespace NetProGroup.Trust.DataMigration
{
    /// <summary>
    /// Service for migrating entities.
    /// </summary>
    public class EntityMigrationService
    {
        private readonly TimeSpan _jobLockRefreshMargin;
        private readonly TimeSpan _migrationRecordRefreshInterval = TimeSpan.FromSeconds(5);
        private readonly ILogger<EntityMigrationService> _logger;
        private readonly IDataMigrationsDataManager _dataMigrationsDataManager;
        private readonly IMongoDbFactory _mongoDbFactory;
        private readonly int _progressUpdateInterval;
        private readonly ILockManager _lockManager;
        private readonly Logging.DataMigrationExcelLogService _excelLogService;
        private readonly bool _storeUnprocessedRecords;

        /// <summary>
        /// Initializes a new instance of the <see cref="EntityMigrationService"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="dataMigrationsDataManager">Instance of the DataMigrationsDataManager.</param>
        /// <param name="mongoDbFactory">Instance of the MongoDB factory.</param>
        /// <param name="appSettings">The data migration application settings.</param>
        /// <param name="lockManager">The lock manager.</param>
        /// <param name="excelLogService">The excel log service.</param>
        public EntityMigrationService(
            ILogger<EntityMigrationService> logger,
            IDataMigrationsDataManager dataMigrationsDataManager,
            IMongoDbFactory mongoDbFactory,
            IOptions<DataMigrationAppSettings> appSettings,
            ILockManager lockManager,
            Logging.DataMigrationExcelLogService excelLogService)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dataMigrationsDataManager = dataMigrationsDataManager ?? throw new ArgumentNullException(nameof(dataMigrationsDataManager));
            _mongoDbFactory = mongoDbFactory ?? throw new ArgumentNullException(nameof(mongoDbFactory));
            _lockManager = lockManager;
            _excelLogService = excelLogService;
            _progressUpdateInterval = appSettings.Value.ProgressUpdateInterval;
            _storeUnprocessedRecords = appSettings.Value.StoreUnprocessedRecords;
            _jobLockRefreshMargin = TimeSpan.FromSeconds(appSettings.Value.JobLockRefreshMarginSeconds);
        }

        /// <summary>
        /// Prepares the entity migration record by setting the initial SourceCount.
        /// </summary>
        /// <typeparam name="T">The type of entity to prepare for migration.</typeparam>
        /// <param name="migrationRecord">The migration record for the process.</param>
        /// <param name="entityName">The name of the entity type.</param>
        /// <param name="entityDisplayName">The display name of the entity type.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task PrepareEntityMigrationAsync(
            Type entityType,
            Domain.DataMigrations.DataMigration migrationRecord,
            string collectionName,
            string entityDisplayName)
        {
            var method = typeof(EntityMigrationService)
                .GetMethod(nameof(PrepareEntityMigrationInternalAsync), BindingFlags.NonPublic | BindingFlags.Instance)
                ?.MakeGenericMethod(entityType);

            if (method == null)
            {
                throw new InvalidOperationException($"Could not find method {nameof(PrepareEntityMigrationInternalAsync)}");
            }

            await (Task)method.Invoke(this, new object[] { migrationRecord, collectionName, entityDisplayName });
        }

        private async Task PrepareEntityMigrationInternalAsync<T>(
            Domain.DataMigrations.DataMigration migrationRecord,
            string collectionName,
            string entityDisplayName)
        {
            _logger.LogInformation("Preparing migration for {EntityName} entities", entityDisplayName);

            var entityProgress = await _dataMigrationsDataManager.GetOrCreateEntityProgressAsync(migrationRecord, entityDisplayName);

            var collection = _mongoDatabase.GetCollection<T>(collectionName);
            var sourceCount = await collection.CountDocumentsAsync(FilterDefinition<T>.Empty);

            entityProgress.SourceCount = (int)sourceCount;
            entityProgress.ProcessedCount = 0;
            entityProgress.SuccessCount = 0;
            entityProgress.FailedCount = 0;

            await _dataMigrationsDataManager.UpdateEntityProgressAsync(entityProgress);

            _logger.LogInformation("Prepared migration for {EntityName} entities. Source count: {SourceCount}", entityDisplayName, sourceCount);
        }

        /// <summary>
        /// Migrates entities of a specific type.
        /// </summary>
        /// <typeparam name="T">The type of entity to migrate.</typeparam>
        /// <param name="migrationRecord"></param>
        /// <param name="jobLock">The job lock.</param>
        /// <param name="entityCollectionName">The name of the entity type.</param>
        /// <param name="entityDisplayName">The display name of the entity type.</param>
        /// <param name="migrationFunc">The function to perform for each entity during migration.</param>
        /// <param name="identifierFunc">The function to get the identifier for an entity.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task MigrateEntityAsync<T>(Domain.DataMigrations.DataMigration migrationRecord,
            LockDTO jobLock,
            string entityCollectionName,
            string entityDisplayName,
            Func<T, Task<(bool Success, List<string> Errors)>> migrationFunc, Func<T, dynamic> identifierFunc) where T : MongoEntityWithId
        {
            _logger.LogInformation("Migrating {EntityName} entities", entityCollectionName);

            var entityProgress = await _dataMigrationsDataManager.GetOrCreateEntityProgressAsync(migrationRecord, entityDisplayName);

            var mongoDatabase = _mongoDbFactory.GetMongoDatabase(migrationRecord.Region);
            var mongoCollection = mongoDatabase.GetCollection<T>(entityCollectionName);

            var totalCount = (int)await mongoCollection.CountDocumentsAsync(FilterDefinition<T>.Empty);
            var processedCount = 0;

            jobLock = await _lockManager.RefreshLockAsync(jobLock.Id.Value);
            var lastMigrationRecordUpdate = DateTime.UtcNow;

            while (processedCount < totalCount)
            {
                using var logWorkbook = await _excelLogService.GetForMigrationRecordAsync(migrationRecord);
                var unprocessedRecords = new List<UnprocessedRecord>();

                // Get batch of documents
                var batch = await mongoCollection
                    .Find(FilterDefinition<T>.Empty, new FindOptions() { BatchSize = _progressUpdateInterval })
                    .Skip(processedCount)
                    .Limit(_progressUpdateInterval)
                    .ToListAsync();

                foreach (var entity in batch)
                {
                    jobLock = await RefreshLockIfNeeded<T>(jobLock);

                    (migrationRecord, lastMigrationRecordUpdate) = await RefreshMigrationRecordIfNeeded(migrationRecord, lastMigrationRecordUpdate);

                    if (migrationRecord.StopRequested)
                    {
                        _logger.LogInformation("Migration stopped by user request");

                        migrationRecord.SetCancelled();
                        await _dataMigrationsDataManager.UpdateMigrationRecordAsync(migrationRecord);
                        await _dataMigrationsDataManager.UpdateEntityProgressAsync(entityProgress);
                        if (_storeUnprocessedRecords)
                        {
                            await _dataMigrationsDataManager.StoreUnprocessedRecordsAsync(migrationRecord, unprocessedRecords);
                        }
                        return;
                    }

                    var idObject = identifierFunc(entity);
                    var identifier = $"{idObject}";
                    using (_logger.BeginScope(idObject))
                    {
                        _logger.LogTrace("Processing {EntityName} {EntityID}", entityCollectionName, identifier);

                        bool success;
                        var errors = new List<string>();
                        try
                        {
                            (success, errors) = await migrationFunc(entity);
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error migrating {EntityName} {EntityID}: {ErrorMessage}", entityCollectionName, identifier, ex.Message);
                            errors.Add(ex.Message);
                            success = false;
                        }

                        entityProgress.ProcessedCount++;
                        processedCount++;

                        if (!success)
                        {
                            _logger.LogWarning("Record with ID: {EntityID} could not be fully processed. Errors: {Errors}", entity.Id, errors);
                            if (_storeUnprocessedRecords)
                            {
                                StoreUnprocessedRecord(unprocessedRecords, string.Join(", ", errors), identifier, entityDisplayName);
                            }

                            entityProgress.FailedCount++;
                        }
                        else
                        {
                            _logger.LogInformation("Successfully processed {EntityName} with ID: {EntityID}", entityCollectionName, identifier);

                            entityProgress.SuccessCount++;
                        }
                        logWorkbook.AddMigratedEntity(entityDisplayName, idObject, success);
                    }
                }

                // Update progress at specified intervals
                await _dataMigrationsDataManager.UpdateEntityProgressAsync(entityProgress);
                if (_storeUnprocessedRecords)
                {
                    await _dataMigrationsDataManager.StoreUnprocessedRecordsAsync(migrationRecord, unprocessedRecords);
                }
                await _excelLogService.SetForMigrationRecordAsync(migrationRecord, logWorkbook, saveChanges: true);
            }
        }

        /// <summary>
        /// Stores an unprocessed record.
        /// </summary>
        /// <param name="unprocessedRecords">The list of unprocessed records.</param>
        /// <param name="reason">The reason for the failure.</param>
        /// <param name="identifier">The identifier of the entity.</param>
        /// <param name="entityDisplayName">The display name of the entity type.</param>
        private static void StoreUnprocessedRecord(List<UnprocessedRecord> unprocessedRecords,
            string reason,
            string identifier,
            string entityDisplayName)
        {
            unprocessedRecords.Add(new UnprocessedRecord
            {
                EntityType = entityDisplayName,
                Identifier = $"{identifier}",
                Reason = reason
            });
        }

        private async Task<LockDTO> RefreshLockIfNeeded<T>(LockDTO jobLock)
        {
            if (jobLock.ExpiresAt - DateTime.UtcNow < _jobLockRefreshMargin)
            {
                _logger.LogInformation("Lock is about to expire, refreshing lock");
                jobLock = await _lockManager.RefreshLockAsync(jobLock.Id.Value);
            }
            else
            {
                _logger.LogTrace("Lock {LockId} valid to {Expiration}, no refresh needed", jobLock.Id, jobLock.ExpiresAt);
            }

            return jobLock;
        }

        private async Task<(Domain.DataMigrations.DataMigration migrationRecord, DateTime lastMigrationRecordUpdate)> RefreshMigrationRecordIfNeeded(
            Domain.DataMigrations.DataMigration migrationRecord,
            DateTime lastMigrationRecordUpdate)
        {
            if (DateTime.UtcNow - lastMigrationRecordUpdate > _migrationRecordRefreshInterval)
            {
                migrationRecord = await _dataMigrationsDataManager.RefreshMigrationRecordAsync(migrationRecord);

                lastMigrationRecordUpdate = DateTime.UtcNow;
            }

            return (migrationRecord, lastMigrationRecordUpdate);
        }
    }
}
