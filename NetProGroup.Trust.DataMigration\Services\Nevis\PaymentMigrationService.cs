using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Domain.Payments.PaymentProvider;
using NetProGroup.Trust.Domain.Submissions;
using PaymentType = NetProGroup.Trust.Domain.Payments.PaymentType;
using NetProGroup.Trust.Domain.Payments.Provider;
using NetProGroup.Trust.DataMigration.Models.Nevis;

namespace NetProGroup.Trust.DataMigration.Services.Nevis
{
    /// <summary>
    /// Service for managing payment-related operations during data migration.
    /// </summary>
    public class PaymentMigrationService
    {
        private readonly ILogger<PaymentMigrationService> _logger;
        private readonly IPaymentRepository _paymentsRepository;
        private readonly IPaymentProviderRepository _paymentProviderRepository;
        private readonly IOptions<DataMigrationAppSettings> _options;

        /// <summary>
        /// Initializes a new instance of the <see cref="PaymentMigrationService"/> class.
        /// </summary>
        /// <param name="logger">The logger instance.</param>
        /// <param name="paymentsRepository">The payments repository.</param>
        /// <param name="paymentProviderRepository">The payment provider repository.</param>
        /// <param name="options">The application settings options.</param>
        public PaymentMigrationService(
            ILogger<PaymentMigrationService> logger,
            IPaymentRepository paymentsRepository,
            IPaymentProviderRepository paymentProviderRepository,
            IOptions<DataMigrationAppSettings> options)

        {
            _logger = logger;
            _paymentsRepository = paymentsRepository;
            _paymentProviderRepository = paymentProviderRepository;
            _options = options;
        }

        /// <summary>
        /// Creates a payment if needed based on the entry data.
        /// </summary>
        /// <param name="entry">The entry containing payment data.</param>
        /// <param name="submission">The submission associated with the payment.</param>
        /// <param name="invoice">The invoice associated with the payment.</param>
        /// <param name="financialYear"></param>
        /// <returns>A tuple containing the created payment (if any) and a list of errors encountered during the process.</returns>
        public async Task<(Domain.Payments.Payment Payment, List<string> Errors)> CreatePaymentIfNeededAsync(
            Entry entry, Submission submission, Invoice invoice, int financialYear)
        {
            var errors = new List<string>();

            var entryPayment = entry.Payment;
            if (entryPayment == null)
            {
                _logger.LogTrace("No payment data found for entry with company code {CompanyCode}, period year {PeriodYear}", entry.Company, entry.PeriodYear);
                return (null, errors);
            }

            if (invoice == null)
            {
                if (financialYear >= 2024)
                {
                    _logger.LogWarning("Not creating payment for entry. Company: {CompanyCode}, Year: {PeriodYear}. Financial year is 2024 or later.", entry.Company, entry.PeriodYear);
                    return (null, errors);
                }

                if (_options.Value.IgnorePaymentWhenNoInvoiceNumber && financialYear == 2019)
                {
                    var errorMessage = "Payment data found but no invoice record found in target database - Ignoring payment due to 2019 and IgnorePaymentWhenNoInvoiceNumber setting";
                    _logger.LogWarning(
                        "{ErrorMessage} - Payment reference: {PaymentReference}, Invoice number: {InvoiceNumber}",
                        errorMessage, entryPayment.PaymentReference, entry.InvoiceNumber);
                }
                else
                {
                    var errorMessage = "Payment data found but no invoice record found in target database";
                    _logger.LogError(
                        "{ErrorMessage} - Payment reference: {PaymentReference}, Invoice number: {InvoiceNumber}",
                        errorMessage, entryPayment.PaymentReference, entry.InvoiceNumber);

                    errors.Add($"{errorMessage}: Payment reference: {entryPayment.PaymentReference}, Invoice number: {entry.InvoiceNumber}");
                }

                return (null, errors);
            }

            _logger.LogTrace("Creating payment for entry with company code {CompanyCode}, period year {PeriodYear}", entry.Company, entry.PeriodYear);

            var paidAt = entryPayment.PaymentReceivedAt;

            var paymentInvoice = invoice.PaymentInvoices.SingleOrDefault();
            if (paymentInvoice == null)
            {
                paymentInvoice = new PaymentInvoice { Invoice = invoice };
                invoice.PaymentInvoices.Add(paymentInvoice);
            }

            // Use existing payment if it exists
            var payment = paymentInvoice.Payment;

            // Find existing payment if none is set yet
            payment ??= await _paymentsRepository.FindFirstOrDefaultByConditionAsync(p =>
                    p.Reference == entryPayment.PaymentReference &&
                    p.LegalEntityId == submission.LegalEntityId &&
                    p.PaidAt == paidAt,
                q => q.Include(p => p.PaymentTransactions));

            // Create new payment if none is found
            payment ??= new Domain.Payments.Payment();
            paymentInvoice.Payment = payment;

            payment.Amount = payment.PaymentInvoices.Sum(pi => pi.Invoice.Amount);
            payment.Currency = invoice.Currency;
            payment.LegalEntityId = submission.LegalEntityId;
            payment.Reference = entryPayment.PaymentReference;
            payment.Status = PaymentStatus.Completed;

            if (entryPayment.PaymentType == "CREDITCARD")
            {
                _logger.LogTrace("Processing credit card payment for entry with company code {CompanyCode}, period year {PeriodYear}", entry.Company, entry.PeriodYear);
                payment.Type = PaymentType.CreditCard;

                var paymentTransaction = payment.PaymentTransactions.SingleOrDefault();
                if (paymentTransaction == null)
                {
                    paymentTransaction = new PaymentTransaction();
                    payment.PaymentTransactions.Add(paymentTransaction);
                }
                paymentTransaction.PaymentProvider = await GetPaymentProvider();
                paymentTransaction.PaidAt = paidAt;
                paymentTransaction.TransactionId = entryPayment.BatchPaymentTransactionId;
            }
            else if (entryPayment.PaymentType == "WIRE TRANSFER")
            {
                _logger.LogTrace("Processing wire transfer payment for entry with company code {CompanyCode}, period year {PeriodYear}", entry.Company, entry.PeriodYear);
                payment.Type = PaymentType.WireTransfer;
                payment.ConfirmedBy = entryPayment.BatchPaymentTransactionId;
            }
            else
            {
                var errorMessage = $"Unknown payment type: {entryPayment.PaymentType}";
                _logger.LogError(errorMessage);
                errors.Add(errorMessage);
            }

            payment.PaidAt = paidAt;

            _logger.LogDebug("Created payment with reference {PaymentReference} for company {CompanyCode}, period year {PeriodYear}", payment.Reference, entry.Company, entry.PeriodYear);
            return (payment, errors);
        }

        /// <summary>
        /// Gets the payment provider.
        /// </summary>
        /// <returns>The payment provider.</returns>
        /// <exception cref="InvalidOperationException">Thrown when no payment provider is found.</exception>
        private async Task<PaymentProvider> GetPaymentProvider()
        {
            var paymentProviders = await _paymentProviderRepository.FindByConditionAsync(p => true);
            var paymentProvider = paymentProviders.SingleOrDefault();

            if (paymentProvider == null)
            {
                _logger.LogError("Payment provider not found");
                throw new InvalidOperationException("Payment provider not found");
            }

            return paymentProvider;
        }
    }
}
