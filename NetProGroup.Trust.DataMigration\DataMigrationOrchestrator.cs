// <copyright file="DataMigrationOrchestrator.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Text.Json;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Trust.DataMigration.Configurations;
using NetProGroup.Trust.DataMigration.Models;

namespace NetProGroup.Trust.DataMigration
{
    /// <summary>
    /// Orchestrator for the data migration process.
    /// </summary>
    public class DataMigrationOrchestrator
    {
        private readonly ILogger<DataMigrationOrchestrator> _logger;
        private readonly IDataMigrationsDataManager _dataMigrationsDataManager;
        private readonly EntityMigrationService _entityMigrationService;
        private readonly ILockManager _lockManager;
        private readonly IServiceProvider _serviceProvider;

        /// <summary>
        /// Initializes a new instance of the <see cref="DataMigrationOrchestrator"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="dataMigrationsDataManager">Instance of the DataMigrationsDataManager.</param>
        /// <param name="entityMigrationService">Instance of the EntityMigrationService.</param>
        /// <param name="lockManager">Instance of the lock manager.</param>
        /// <param name="serviceProvider">Instance of the service provider.</param>
        public DataMigrationOrchestrator(
            ILogger<DataMigrationOrchestrator> logger,
            IDataMigrationsDataManager dataMigrationsDataManager,
            EntityMigrationService entityMigrationService,
            ILockManager lockManager,
            IServiceProvider serviceProvider)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _dataMigrationsDataManager = dataMigrationsDataManager ?? throw new ArgumentNullException(nameof(dataMigrationsDataManager));
            _entityMigrationService = entityMigrationService ?? throw new ArgumentNullException(nameof(entityMigrationService));
            _lockManager = lockManager ?? throw new ArgumentNullException(nameof(lockManager));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
        }

        /// <summary>
        /// Performs the data migration process for a given region.
        /// </summary>
        /// <param name="migrationRecord">The migration record for the process.</param>
        /// <param name="jobLock">The job lock.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task PerformDataMigrationAsync(Domain.DataMigrations.DataMigration migrationRecord,
            LockDTO jobLock)
        {
            try
            {
                _logger.LogInformation("Starting data migration for region: {Region}", migrationRecord.Region);
                migrationRecord.SetInProgress();
                await _dataMigrationsDataManager.UpdateMigrationRecordAsync(migrationRecord);

                // Prepare entity migration records
                _logger.LogInformation("Preparing entity migration records");
                await PrepareEntityMigrationRecords(migrationRecord, jobLock);

                // Perform migrations
                _logger.LogInformation("Performing migrations");
                await PerformMigrations(migrationRecord, jobLock);

                // Check for unprocessed records and update status if necessary
                CheckUnprocessedRecords(migrationRecord);

                if (!migrationRecord.IsFailed() && !migrationRecord.IsCancelled())
                {
                    _logger.LogInformation("Migration completed successfully.");

                    migrationRecord.SetCompleted();
                }
                else
                {
                    _logger.LogWarning("Migration failed.");
                }

                _logger.LogInformation("Data migration completed with status {Status} for region: {Region}", migrationRecord.Status, migrationRecord.Region);
                await _dataMigrationsDataManager.UpdateMigrationRecordAsync(migrationRecord);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during data migration for region: {Region}", migrationRecord.Region);
                throw;
            }
        }

        private async Task PrepareEntityMigrationRecords(Domain.DataMigrations.DataMigration migrationRecord,
            LockDTO jobLock)
        {
            await RefreshLock(jobLock);

            var migrationSteps = GetMigrationStepsForJurisdiction(migrationRecord.Region);
            foreach (var entityMigrationStep in migrationSteps)
            {
                await _entityMigrationService.PrepareEntityMigrationAsync(
                    entityMigrationStep.EntityType,
                    migrationRecord,
                    entityMigrationStep.CollectionName,
                    entityMigrationStep.DisplayName);
            }
        }

        private async Task PerformMigrations(Domain.DataMigrations.DataMigration migrationRecord, LockDTO jobLock)
        {
            await RefreshLock(jobLock);

            var migrationStepsForJurisdiction = GetMigrationStepsForJurisdiction(migrationRecord.Region);
            foreach (var step in migrationStepsForJurisdiction)
            {
                var service = (IEntityMigrator)_serviceProvider.GetRequiredService(step.MigrationServiceType);
                await service.MigrateEntitiesAsync(migrationRecord, jobLock);

                await RefreshLock(jobLock);
            }
        }

        private async Task RefreshLock(LockDTO jobLock)
        {
            if (!(jobLock.ExpiresAt - DateTime.UtcNow > TimeSpan.FromSeconds(30)))
            {
                await _lockManager.RefreshLockAsync(jobLock.Id.Value);
            }
        }

        private void CheckUnprocessedRecords(Domain.DataMigrations.DataMigration migrationRecord)
        {
            if (!string.IsNullOrEmpty(migrationRecord.UnprocessedRecords))
            {
                var unprocessedRecords = JsonSerializer.Deserialize<List<UnprocessedRecord>>(migrationRecord.UnprocessedRecords);
                if (unprocessedRecords is { Count: > 0 })
                {
                    migrationRecord.SetFailed("Migration completed with unprocessed records.");
                    _logger.LogWarning("Migration completed with {FailedCount} unprocessed records. Migration marked as failed.", unprocessedRecords.Count);
                }
            }
        }

        private IEnumerable<EntityMigrationStep> GetMigrationStepsForJurisdiction(string region)
        {
            return RegionMigrationConfigRegistry.MigrationSteps[region];
        }
    }
}
