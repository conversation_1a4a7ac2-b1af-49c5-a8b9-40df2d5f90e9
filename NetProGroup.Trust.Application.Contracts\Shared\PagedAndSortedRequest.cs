﻿// <copyright file="PagedAndSortedRequest.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Application.Contracts.Shared
{
    /// <summary>
    /// Base class for a paged request.
    /// </summary>
    public abstract class PagedAndSortedRequest : PagedRequest
    {
        /// <summary>
        /// Gets or sets the name of the field to sort on.
        /// </summary>
        public virtual string SortBy { get; set; }

        /// <summary>
        /// Gets or sets in which order to sort (asc or desc).
        /// </summary>
        public string SortOrder { get; set; }
    }
}
