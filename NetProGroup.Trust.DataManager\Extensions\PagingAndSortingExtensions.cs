﻿using NetProGroup.Framework.Paging;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Defines;

namespace NetProGroup.Trust.DataManager.Extensions
{
    public static class PagingAndSortingExtensions
    {
        /// <summary>
        /// Vaidates the PagingInfo.
        /// </summary>
        /// <param name="value"></param>
        public static void ValidatePagingInfo(this PagingInfo value)
        {
            if (value != null)
            {
                if (value.PageNumber == 0)
                {
                    value.PageNumber = PagingSettings.DefaultPageNumber;
                }
                if (value.PageSize == 0)
                {
                    value.PageSize = PagingSettings.DefaultPageSize;
                }

                Check.Positive(value.PageNumber, nameof(value.PageNumber));
                Check.Positive(value.PageSize, nameof(value.PageSize));
            }
        }

        /// <summary>
        /// Creates a PagingInfo from the PagedRequest.
        /// </summary>
        /// <param name="request">The request containing paging information.</param>
        /// <returns>A PagingInfo object with validated paging parameters.</returns>
        public static PagingInfo ToPagingInfo(this PagedRequest request)
        {
            return new PagingInfo(request.PageNumber, request.PageSize);
        }

        /// <summary>
        /// Creates a SortingInfo from the PagedAndSortedRequest.
        /// </summary>
        /// <param name="request">The request containing sorting information.</param>
        /// <returns>A SortingInfo object with validated sorting parameters.</returns>
        public static SortingInfo ToSortingInfo(this PagedAndSortedRequest request)
        {
            return new SortingInfo() { SortBy = request.SortBy, SortOrder = request.SortOrder }.Validate();
        }

        public static SortingInfo Validate(this SortingInfo sortingInfo)
        {
            if (sortingInfo == null)
            {
                sortingInfo = new SortingInfo();
            }

            if (string.IsNullOrEmpty(sortingInfo.SortOrder))
            {
                sortingInfo.SortOrder = OrderByDefines.Ascending;
            }

            if (sortingInfo.SortBy == null)
            {
                sortingInfo.SortBy = string.Empty;
            }

            return sortingInfo;
        }
    }
}
