using NetProGroup.Trust.DataMigration.Models;
using NetProGroup.Trust.DataMigration.Models.Nevis;
using NetProGroup.Trust.DataMigration.Services.Nevis;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.DataMigration.Configurations
{
    public static class RegionMigrationConfigRegistry
    {
        public static Dictionary<string, IEnumerable<EntityMigrationStep>> MigrationSteps { get; } = new()
        {
            {
                JurisdictionCodes.Nevis,
                new List<EntityMigrationStep>
                {
                    new EntityMigrationStep(
                        typeof(NevisInvoiceConfiguration),
                        MigrationConsts.NevisInvoiceConfigurations.CollectionName,
                        MigrationConsts.NevisInvoiceConfigurations.DisplayName,
                        typeof(InvoiceConfigurationsMigrationService)),
                    new EntityMigrationStep(
                        typeof(Entry),
                        MigrationConsts.Entries.CollectionName,
                        MigrationConsts.Entries.DisplayName,
                        typeof(EntryMigrationServiceWrapper)),
                    new EntityMigrationStep(
                        typeof(Company),
                        MigrationConsts.Companies.CollectionName,
                        MigrationConsts.Companies.DisplayName,
                        typeof(CompanyMigrationServiceWrapper))
                }
            },
            {
                JurisdictionCodes.Bahamas,
                new List<EntityMigrationStep>()
            }
        };
    }
}
