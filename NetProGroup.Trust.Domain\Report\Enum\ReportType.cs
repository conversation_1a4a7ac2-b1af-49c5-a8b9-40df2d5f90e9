// <copyright file="ReportType.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Report.Enum
{
    /// <summary>
    /// The type of report.
    /// </summary>
    public enum ReportType
    {
        /// <summary>
        /// Submission invoices.
        /// </summary>
        Financial = 0,

        /// <summary>
        /// Companies that do not have a submission.
        /// </summary>
        CompaniesWithoutSubmissions = 1,

        /// <summary>
        /// Submissions that have not been paid.
        /// </summary>
        SubmissionsNotPaid = 2,

        /// <summary>
        /// All master client users.
        /// </summary>
        ContactsInfo = 3,

        /// <summary>
        /// Financial report for Panama submissions.
        /// </summary>
        BasicFinancialReport = 4,

        /// <summary>
        /// Financial report for Bahamas submissions.
        /// </summary>
        EconomicSubstance = 5
    }
}