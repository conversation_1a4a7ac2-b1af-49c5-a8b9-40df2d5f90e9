﻿using ClosedXML.Excel;
using DocumentFormat.OpenXml.Wordprocessing;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Graph.Models;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Submissions;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Reports.Nevis.SubmissionsNotPaid;
using NetProGroup.Trust.Tests.Shared;
using NUnit.Framework.Internal;

namespace NetProGroup.Trust.Tests.Reports.Nevis.SubmissionNotPaid
{
    [TestFixture()]
    public class SubmissionNotPaidGeneratorTests : TestBase
    {
        private ISubmissionsNotPaidReportGenerator _submissionsNotPaidReportGenerator;
        private ISubmissionsRepository _submissionsRepository;
        private ILegalEntitiesRepository _legalEntitiesRepository;
        private List<LegalEntity> _legalEntities;

        [SetUp]
        public async Task Setup()
        {
            _submissionsNotPaidReportGenerator = _server.Services.GetService<ISubmissionsNotPaidReportGenerator>();
            _submissionsRepository = _server.Services.GetService<ISubmissionsRepository>();
            _legalEntitiesRepository = _server.Services.GetService<ILegalEntitiesRepository>();
            await Seed();
        }

        public async Task Seed()
        {
            _legalEntities = new List<LegalEntity>
            {
                new LegalEntity
                {
                    MasterClientId = _masterClient.Id,
                    Name = "Company Test 1",
                    Code = "E-LL 1",
                    EntityType = LegalEntityType.Company,
                    IncorporationDate = DateTime.UtcNow.AddYears(-2),
                    JurisdictionId = JurisdictionNevisId
                },
                new LegalEntity
                {
                    MasterClientId = _masterClient.Id,
                    Name = "Company Test 2",
                    Code = "E-LL 2",
                    EntityType = LegalEntityType.Company,
                    IncorporationDate = DateTime.UtcNow.AddYears(-2),
                    JurisdictionId = JurisdictionNevisId
                }
            };

            foreach (var legalEntity in _legalEntities)
            {
                await _legalEntitiesRepository.InsertAsync(legalEntity, true);
            }

            // Seed data for testing
            var submission = new Submission
            {
                LegalEntityId = _legalEntities[0].Id,
                Status = SubmissionStatus.Submitted,
                Name = "Test Submission",
                Layout = "Test",
                ReportId = "TestReportId"
            };

            await _submissionsRepository.InsertAsync(submission, true);
        }

        [Test]
        public async Task GenerateSubmissionsNotPaidReportAsync_ShouldOnlyIncludeEntitiesWithSubmissions()
        {
            // Act
            var result = await _submissionsNotPaidReportGenerator.GenerateSubmissionsNotPaidReportAsync();

            // Assert
            result.Should().NotBeNull();

            result.LegalEntities.Count().Should().Be(1, "Only one seeded legal Company should be in the resultant report 'Company Test 1' (_legalEntities[0]).");
            result.LegalEntities.Single().Id.Should().Be(_legalEntities[0].Id, "The first legal entity in the report should be the one seeded.");
            result.FileContent.Should().NotBeNullOrEmpty("The report should have content");

            using var fileContent = new MemoryStream(result.FileContent);
            using var resultWorkbook = new XLWorkbook(fileContent);
            var worksheet = resultWorkbook.Worksheet(1);
            var lastUsedRow = worksheet.LastRowUsed()?.RowNumber() ?? 0;
            lastUsedRow.Should().Be(2, "There should be only one data row below the header in the Excel report (Just the first seeded company).");
            var cellValue = worksheet.Cell(2, 1).Value.ToString();
            cellValue.Should().Be(_legalEntities.First().Name);
        }
    }
}
