using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Nevis
{
    /// <summary>
    /// Represents corporation-related information in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class CorporationRelated
    {
        /// <summary>
        /// Gets or sets a value indicating whether the corporation is part of a multinational enterprise group.
        /// </summary>
        [BsonElement("is_corporation_related")]
        public bool IsCorporationRelated { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the corporation's group has a consolidated gross turnover exceeding 750 million.
        /// </summary>
        [BsonElement("is_gross_turnover")]
        public bool? IsGrossTurnover { get; set; }
    }
}
