const fs = require('fs');
const path = require('path');

// Simple regex-based approach for XML parsing
module.exports.readVersion = function (contents) {
  try {
    // Look for FileVersion tag in the XML
    const match = contents.match(/<FileVersion>(.*?)<\/FileVersion>/);
    if (match && match[1]) {
      return match[1];
    }
    return '0.0.0';
  } catch (error) {
    console.error('Error reading FileVersion from csproj:', error);
    return '0.0.0';
  }
};

module.exports.writeVersion = function (contents, version) {
  try {
    // If FileVersion tag exists, replace its value
    if (contents.match(/<FileVersion>.*?<\/FileVersion>/)) {
      return contents.replace(
        /<FileVersion>.*?<\/FileVersion>/,
        `<FileVersion>${version}</FileVersion>`
      );
    } 
    // Otherwise, add it after the first PropertyGroup opening tag
    else {
      return contents.replace(
        /<PropertyGroup>/,
        `<PropertyGroup>\n    <FileVersion>${version}</FileVersion>`
      );
    }
  } catch (error) {
    console.error('Error writing version to csproj:', error);
    return contents;
  }
};
