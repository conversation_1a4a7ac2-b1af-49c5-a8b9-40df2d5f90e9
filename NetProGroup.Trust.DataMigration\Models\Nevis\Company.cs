using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Nevis
{
    /// <summary>
    /// Represents a company entity for data migration purposes.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class Company : MongoEntityWithId
    {
        /// <summary>
        /// Gets or sets the name of the company.
        /// </summary>
        [BsonElement("name")]
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the address of the company.
        /// </summary>
        [BsonElement("address")]
        public string Address { get; set; }

        /// <summary>
        /// Gets or sets the code of the company.
        /// </summary>
        [BsonElement("code")]
        public string Code { get; set; }

        /// <summary>
        /// Gets or sets the VP code of the company.
        /// </summary>
        [BsonElement("vpcode")]
        public string VpCode { get; set; }

        /// <summary>
        /// Gets or sets the incorporation code of the company.
        /// </summary>
        [BsonElement("incorporationcode")]
        public string IncorporationCode { get; set; }

        /// <summary>
        /// Gets or sets the master client code of the company.
        /// </summary>
        [BsonElement("masterclientcode")]
        public string MasterClientCode { get; set; }

        /// <summary>
        /// Gets or sets the referral office of the company.
        /// </summary>
        [BsonElement("referral_office")]
        public string ReferralOffice { get; set; }

        /// <summary>
        /// Gets or sets the amount associated with the company.
        /// </summary>
        [BsonElement("amount")]
        public decimal Amount { get; set; }

        /// <summary>
        /// Gets or sets the partition key of the company.
        /// </summary>
        [BsonElement("partitionkey")]
        public string PartitionKey { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the company is deleted.
        /// </summary>
        [BsonElement("isDeleted")]
        public bool IsDeleted { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the company was deleted.
        /// </summary>
        [BsonElement("deletedAt")]
        public DateTime? DeletedAt { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the company has a late payment exception.
        /// </summary>
        [BsonElement("hasLatePaymentException")]
        public bool HasLatePaymentException { get; set; }
    }
}
