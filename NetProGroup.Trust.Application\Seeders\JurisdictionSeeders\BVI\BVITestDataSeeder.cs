﻿// <copyright file="BVITestDataSeeder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Bogus;
using Microsoft.Extensions.Logging;
using NetProGroup.Trust.Application.AppServices.Tools;
using NetProGroup.Trust.DataManager.LegalEntities;
using NetProGroup.Trust.DataManager.LegalEntityRelations;
using NetProGroup.Trust.DataManager.LegalEntityRelations.BeneficialOwners.RequestResponses;
using NetProGroup.Trust.DataManager.LegalEntityRelations.Directors.RequestResponses;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Repository.Sync;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Jurisdictions;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Application.Seeders.JurisdictionSeeders.BVI
{
    /// <summary>
    /// Seeder for Bahamas data.
    /// </summary>
    public class BVITestDataSeeder : SeederBase, IBVITestDataSeeder
    {
        private readonly ILogger _logger;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly ILegalEntitiesDataManager _legalEntitiesDataManager;
        private readonly IBeneficialOwnersDataManager _beneficialOwnersDataManager;
        private readonly IDirectorsDataManager _directorsDataManager;

        private Jurisdiction _jurisdiction;
        private readonly Faker<SyncBeneficialOwner> _boFaker;

        private const string CompanyCode1 = "VG_BVI_1";
        private const string CompanyCode2 = "VG_BVI_2";

        private const string MasterClientCode1 = "MC_BVI_1";
        private const string MasterClientCode2 = "MC_BVI_2";

        /// <summary>
        /// Initializes a new instance of the <see cref="BVITestDataSeeder"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="serviceProvider">Instance of the serviceProvider.</param>
        /// <param name="jurisdictionsRepository">Instance of the jurisdiction repository.</param>
        /// <param name="legalEntitiesDataManager">Instance of the legalentities datamanager.</param>
        /// <param name="beneficialOwnersDataManager">Instance of the beneficialowners datamanager.</param>
        /// <param name="directorsDataManager">Instance of the directors datamanager.</param>
        public BVITestDataSeeder(ILogger<BVITestDataSeeder> logger,
                                 IServiceProvider serviceProvider,
                                 IJurisdictionsRepository jurisdictionsRepository,
                                 ILegalEntitiesDataManager legalEntitiesDataManager,
                                 IBeneficialOwnersDataManager beneficialOwnersDataManager,
                                 IDirectorsDataManager directorsDataManager)
            : base(logger, serviceProvider)
        {
            _logger = logger;
            _jurisdictionsRepository = jurisdictionsRepository;

            _legalEntitiesDataManager = legalEntitiesDataManager;
            _beneficialOwnersDataManager = beneficialOwnersDataManager;
            _directorsDataManager = directorsDataManager;

            _boFaker = BoFaker.Create();
        }

        /// <inheritdoc/>
        public async Task RunAsync()
        {
            _jurisdiction = await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(j => j.Code == JurisdictionCodes.BritishVirginIslands);

            try
            {
                await SyncHelper.LockAsync();
                SyncHelper.JurisdictionCodes = new List<string> { JurisdictionVPCodes.BritishVirginIslands };

                await CreateMasterClientsAsync();
                await CreateCompaniesAsync();
                await CreateBOsAsync();
                await CreateDirectorsAsync();
            }
            finally
            {
                SyncHelper.Unlock();
            }
        }

        private async Task CreateMasterClientsAsync()
        {
            await CreateMasterClientAsync(MasterClientCode1);
            await AssignUsersToMasterClientAsync(MasterClientCode1);

            await CreateMasterClientAsync(MasterClientCode2);
            await AssignUsersToMasterClientAsync(MasterClientCode2);
        }

        private async Task CreateCompaniesAsync()
        {
            var masterClientCode = string.Empty;


            await CreateCompanyAsync(CompanyCode1, "BVI 1", "BVIP8347", MasterClientCode1);
            await CreateCompanyAsync(CompanyCode1, "BVI 1", "BVIP8347", MasterClientCode2);

            await CreateCompanyAsync(CompanyCode2, "BVI 2", "BVIJ4427", MasterClientCode2);
        }

        private async Task CreateBOsAsync()
        {
            var syncBOs = new List<SyncBeneficialOwner>();

            var individualFaker = _boFaker
                .RuleFor(owner => owner.OfficerTypeCode, "VGPT01")
                .RuleFor(owner => owner.FileType, "individual");

            var richardScott = individualFaker
                .RuleFor(owner => owner.UniqueRelationId, "BVI_VG00335908-VG00396741-1")
                .RuleFor(owner => owner.Name, "BVI_QICOFI MAHELOD")
                .RuleFor(owner => owner.CompanyNumber, CompanyCode1)
                .RuleFor(owner => owner.EntityCode, CompanyCode1)
                .RuleFor(owner => owner.DateOfBirthOrIncorp, new DateTime(1954, 8, 15))
                .RuleFor(owner => owner.PlaceOfBirthOrIncorp, "United Kingdom")
                .RuleFor(owner => owner.Nationality, "British")
                .RuleFor(owner => owner.ResidentialOrRegisteredAddress, "Tramman, 5 Beach Road\r\nPort St Mary\r\nIM9 5NG\r\nIsle of Man")
                .RuleFor(owner => owner.TIN, string.Empty)
                .Generate();
            syncBOs.Add(richardScott);

            var davidHermanus = individualFaker
                .RuleFor(owner => owner.UniqueRelationId, "BVI_VG00513737-VG00557189-2")
                .RuleFor(owner => owner.Name, "BVI_DAJAPDA SIKAQBMAA PEBOK")
                .RuleFor(owner => owner.CompanyNumber, CompanyCode1)
                .RuleFor(owner => owner.EntityCode, CompanyCode1)
                .RuleFor(owner => owner.DateOfBirthOrIncorp, new DateTime(1962, 5, 16))
                .RuleFor(owner => owner.PlaceOfBirthOrIncorp, "South Africa")
                .RuleFor(owner => owner.Nationality, "British")
                .RuleFor(owner => owner.ResidentialOrRegisteredAddress, "Woodlee, Hillberry Green\r\nDouglas\r\nIsle of Man IM2 6DE")
                .RuleFor(owner => owner.TIN, string.Empty)
                .Generate();
            syncBOs.Add(davidHermanus);

            var sarahMargaret = individualFaker
                .RuleFor(owner => owner.UniqueRelationId, "BVI_VG00568003-**********-3")
                .RuleFor(owner => owner.Name, "Babijase Hejov Moti Rideho")
                .RuleFor(owner => owner.CompanyNumber, CompanyCode2)
                .RuleFor(owner => owner.EntityCode, CompanyCode2)
                .RuleFor(owner => owner.DateOfBirthOrIncorp, new DateTime(1957, 6, 14))
                .RuleFor(owner => owner.PlaceOfBirthOrIncorp, "United Kingdom")
                .RuleFor(owner => owner.Nationality, "British")
                .RuleFor(owner => owner.ResidentialOrRegisteredAddress, "Harrigan Estate\r\nTortola\r\nVG1110\r\nBritish Virgin Islands")
                .RuleFor(owner => owner.TIN, string.Empty)
                .Generate();
            syncBOs.Add(sarahMargaret);

            var cnpc = _boFaker
                .RuleFor(owner => owner.UniqueRelationId, "BVI_VG00349251-PA00000252-7")
                .RuleFor(owner => owner.Name, "Kjacatq Kelijb Veicis")
                .RuleFor(owner => owner.CompanyNumber, CompanyCode2)
                .RuleFor(owner => owner.EntityCode, CompanyCode2)
                .RuleFor(owner => owner.FileType, "company")
                .RuleFor(owner => owner.OfficerTypeCode, "VGPT02")
                .RuleFor(owner => owner.DateOfBirthOrIncorp, new DateTime(1990, 2, 9))
                .RuleFor(owner => owner.Country, "China")
                .RuleFor(owner => owner.JurisdictionOfRegulationOrSovereignState, "China")
                .RuleFor(owner => owner.ResidentialOrRegisteredAddress, "9 Dongzhimen North Street, Dongcheng District, Beijing\r\nChina")
                .RuleFor(owner => owner.TIN, string.Empty)
                .Generate();
            syncBOs.Add(cnpc);

            await _beneficialOwnersDataManager.SyncBeneficialOwnersAsync(new SyncBeneficialOwnerRequest { ChangedBeneficialOwners = syncBOs });
        }

        private async Task CreateDirectorsAsync()
        {
            var companyCode = string.Empty;
            var syncDirectors = new List<SyncDirector>();

            syncDirectors.Add(new SyncDirector
            {
                UniqueRelationId = "BVI_VG00061604-PA00001261-5",
                FileType = "company",
                RelationType = "Director",
                CompanyNumber = CompanyCode1,
                EntityCode = CompanyCode1,
                Name = "TQINE HAJIJIKOGG JDEAX SEHF",
                IncorporationNumberOrPassportNr = "C18834",
                PlaceOfBirthOrIncorp = "Saint Kitts and Nevis",
                DateOfBirthOrIncorp = new DateTime(2000, 11, 28),
                FromDate = new DateTime(2020, 6, 9),
                ToDate = null,
                ResidentialOrRegisteredAddress = "Hunkins Waterfront Plaza\r\nSuite 556\r\nMain Street\r\nCharlestown\r\nNevis\r\nSaint Kitts and Nevis"
            });

            syncDirectors.Add(new SyncDirector
            {
                UniqueRelationId = "BVI_VG00219380-PA00000575-3",
                FileType = "company",
                RelationType = "Director",
                CompanyNumber = CompanyCode1,
                EntityCode = CompanyCode1,
                Name = "EBCIEKA W.E.",
                IncorporationNumberOrPassportNr = "C18832",
                PlaceOfBirthOrIncorp = "Saint Kitts and Nevis",
                DateOfBirthOrIncorp = new DateTime(2000, 11, 28),
                FromDate = new DateTime(2020, 6, 9),
                ToDate = null,
                ResidentialOrRegisteredAddress = "Hunkins Waterfront Plaza\r\nSuite 556\r\nMain Street\r\nCharlestown\r\nNevis\r\nSaint Kitts and Nevis"
            });

            syncDirectors.Add(new SyncDirector
            {
                UniqueRelationId = "BVI_VG00164266-PA00001257-4",
                FileType = "individual",
                RelationType = "Director",
                CompanyNumber = CompanyCode2,
                EntityCode = CompanyCode2,
                Name = "HHITYOQK KICTEOQ TEKVIV",
                FormerName = "",
                DateOfBirthOrIncorp = new DateTime(1957, 6, 14),
                PlaceOfBirthOrIncorp = "United Kingdom",
                ServiceAddress = "",
                ResidentialOrRegisteredAddress = "Harrigan Estate\r\nTortola\r\nVG1110\r\nBritish Virgin Islands",
                FromDate = new DateTime(1900, 1, 1),
                ToDate = null,
                Nationality = "British"
            });

            syncDirectors.Add(new SyncDirector
            {
                UniqueRelationId = "BVI_VG00074124-PA00001279-5",
                FileType = "individual",
                RelationType = "Director",
                CompanyNumber = CompanyCode2,
                EntityCode = CompanyCode2,
                Name = "RKFI Tavletifi Cadgicif Wagimiw",
                FormerName = "",
                DateOfBirthOrIncorp = new DateTime(1954, 10, 12),
                PlaceOfBirthOrIncorp = "",
                ServiceAddress = "",
                ResidentialOrRegisteredAddress = "",
                FromDate = new DateTime(1900, 1, 1),
                ToDate = null,
                Nationality = "United Kingdom"
            });

            await _directorsDataManager.SyncDirectorsAsync(new SyncDirectorRequest { ChangedDirectors = syncDirectors });
        }

        private async Task CreateCompanyAsync(string code, string name, string incorporationNr, string masterClientCode)
        {
            var request = new DataManager.LegalEntities.Models.SyncLegalEntitiesRequest();

            var legalEntity = new DataManager.LegalEntities.Models.SyncLegalEntity
            {
                JurisdictionCode = _jurisdiction.Code,
                UniqueId = code,
                EntityType = DomainShared.Enums.LegalEntityType.Company,
                MasterClientCode = masterClientCode,
                Code = code,
                Name = name,
                IncorporationNr = incorporationNr,
                LegacyCode = $"legacy {code}",
                ReferralOffice = "REF1",
                EntityStatusCode = LegalEntityStatusCodes.Active
            };
            request.LegalEntities.Add(legalEntity);

            await _legalEntitiesDataManager.SyncLegalEntitiesAsync(request);
        }

    }
}
