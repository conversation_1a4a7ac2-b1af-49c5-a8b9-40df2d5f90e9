using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Nevis
{
    /// <summary>
    /// Represents a company representative in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class CompanyRepresentative
    {
        /// <summary>
        /// Gets or sets the name of the company representative.
        /// </summary>
        [BsonElement("name")]
        public string Name { get; set; }

        /// <summary>
        /// Gets or sets the telephone number of the company representative.
        /// </summary>
        [BsonElement("telephone")]
        public string Telephone { get; set; }

        /// <summary>
        /// Gets or sets the fax number of the company representative.
        /// </summary>
        [BsonElement("fax")]
        public string Fax { get; set; }

        /// <summary>
        /// Gets or sets the email address of the company representative.
        /// </summary>
        [BsonElement("email")]
        public string Email { get; set; }
    }
}
