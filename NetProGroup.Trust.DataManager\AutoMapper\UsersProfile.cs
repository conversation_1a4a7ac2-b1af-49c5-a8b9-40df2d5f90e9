﻿// <copyright file="UsersProfile.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Trust.Application.Contracts.Users;
using NetProGroup.Trust.DataManager.Users;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Shared.Roles;
using System.Linq.Expressions;

namespace NetProGroup.Trust.DataManager.AutoMapper
{
    /// <summary>
    /// The users profile for AutoMapper.
    /// </summary>
    public class UsersProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="UsersProfile"/> class.
        /// </summary>
        public UsersProfile()
        {
            Expression<Func<List<string>, string>> primaryRoleLabelExpression =
                roleNames =>
                    roleNames.Count == 0 ?
                        null :
                        roleNames.Contains(WellKnownRoleNames.Client) ?
                            "Client portal user" :
                            "Management portal user";

            var primaryRoleLabel = ExpressionExtensions.Combine<UsersDataManager.UserDto, List<string>, string>(
                source => source.RoleNames.Select(role => role).ToList(),
                primaryRoleLabelExpression);

            Expression<Func<ApplicationUser, string>> emailExpression = src => src.Email == null ? string.Empty : src.Email.ToLower();

            // We never actually map directly from ApplicationUser to PCPApplicationUserDTO. This is used in the mapping from UserDTO to PCPApplicationUserDTO,
            // So that we don't have to map from src.User.Email, but can just say IncludeMembers(dto => dto.User) in the mapping from UserDto to PCPApplicationUserDTO,
            // And that will handle the mapping of the email property (and others).
            CreateMap<ApplicationUser, PCPApplicationUserDTO>()
               .ForMember(dest => dest.IsBlocked, opt => opt.MapFrom(src => src.LockoutEnabled))
               .ForMember(dest => dest.Email, opt => opt.MapFrom(emailExpression))
               .ForMember(dest => dest.PrimaryRoleLabel, opt => opt.Ignore())
               .ForMember(dest => dest.Permissions, opt => opt.Ignore())
               .ForMember(dest => dest.ApplicationUserRoles, opt => opt.Ignore())
               .ForMember(dest => dest.RoleIds, opt => opt.Ignore())
               .ForMember(dest => dest.RoleNames, opt => opt.Ignore());

            CreateMap<UsersDataManager.UserDto, PCPApplicationUserDTO>()
                .IncludeMembers(dto => dto.User)
                .ForMember(dest => dest.PrimaryRoleLabel, opt => opt.MapFrom(primaryRoleLabel))
                .ForMember(dest => dest.Permissions, opt => opt.Ignore())
                .ForMember(dest => dest.ApplicationUserRoles, opt => opt.Ignore())
                .ForMember(dest => dest.RoleIds, opt => opt.Ignore());

            CreateMap<ApplicationUser, ListApplicationUsersDTO>()
                .ForMember(dest => dest.PrimaryRoleLabel, opt => opt.Ignore())
                .ForMember(dest => dest.IsBlocked, opt => opt.MapFrom(src => src.LockoutEnabled))
                .ForMember(dest => dest.Email, opt => opt.MapFrom(emailExpression));

            CreateMap<UsersDataManager.UserDto, ListApplicationUsersDTO>()
                .IncludeMembers(dto => dto.User)
                .ForMember(dest => dest.PrimaryRoleLabel, opt => opt.MapFrom(primaryRoleLabel));
        }
    }
}
