using NetProGroup.Trust.Reports.Nevis.ExportSubmissionIRD.Annual;

namespace NetProGroup.Trust.Tests.Reports.Nevis.ExportSubmissionIRD.Annual
{
    [TestFixture()]
    public class Export2023SubmissionsIRDGeneratorTests : BaseExportSubmissionsIRDGeneratorTests<IExport2023SubmissionsIRDGenerator>
    {
        public override void Setup()
        {
            base.Setup();

            // Set the financial year for 2023
            FinancialYear = 2023;
        }

        /// <summary>
        /// Tests that when the TaxResidentResidentCountry field is empty, the exported Excel file
        /// shows "No Country" in the appropriate cell (Question 1.2).
        /// </summary>
        [Test]
        public async Task Question_1_2_EmptyTaxResidentCountry_ReturnsNoCountry()
        {
            // For 2023, the country value is in cell (3, 50)
            await TestEmptyTaxResidentCountryReturnsNoCountry(3, 50);
        }
    }
}
