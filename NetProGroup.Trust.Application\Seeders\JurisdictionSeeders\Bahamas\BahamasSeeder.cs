﻿// <copyright file="BahamasSeeder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using NetProGroup.Trust.Application.Common;
using NetProGroup.Trust.DataManager.Forms;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Shared.Modules;
using NetProGroup.Trust.Shared.Jurisdictions;

namespace NetProGroup.Trust.Application.Seeders.JurisdictionSeeders.Bahamas
{
    /// <summary>
    /// Seeder for Bahamas data.
    /// </summary>
    public class BahamasSeeder : SeederBase, IBahamasSeeder
    {
        private readonly ILogger _logger;
        private readonly IServiceProvider _serviceProvider;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly ISettingsManager _settingsManager;
        private readonly AppSettings _appSettings;

        private Jurisdiction _jurisdiction;

        /// <summary>
        /// Initializes a new instance of the <see cref="BahamasSeeder"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="serviceProvider">Instance of the serviceProvider.</param>
        /// <param name="settingsManager">Instance of the settings manager.</param>
        /// <param name="jurisdictionsRepository">Instance of the jurisdiction repository.</param>
        /// <param name="options">Instance of the app settings.</param>
        public BahamasSeeder(ILogger<BahamasSeeder> logger,
                           IServiceProvider serviceProvider,
                           ISettingsManager settingsManager,
                           IJurisdictionsRepository jurisdictionsRepository,
                           IOptions<AppSettings> options)
            : base(logger, serviceProvider)
        {
            ArgumentNullException.ThrowIfNull(options);

            _logger = logger;
            _serviceProvider = serviceProvider;
            _settingsManager = settingsManager;
            _jurisdictionsRepository = jurisdictionsRepository;
            _appSettings = options.Value;
        }

        /// <inheritdoc/>
        public async Task RunAsync()
        {
            _jurisdiction = await _jurisdictionsRepository.FindSingleOrDefaultByConditionAsync(j => j.Code == JurisdictionCodes.Bahamas);

            await AssignModuleToJurisdictionAsync(ModuleKeyConsts.EconomicSubstanceBahamas, JurisdictionCodes.Bahamas);
            await AssignModuleToJurisdictionAsync(ModuleKeyConsts.BODirectors, JurisdictionCodes.Bahamas);

            await CreateFormTemplatesAsync();

            if (_appSettings.SeedTestDataOnStartup)
            {
                var testDataSeeder = _serviceProvider.GetRequiredService<IBahamasTestDataSeeder>();
                await testDataSeeder.RunAsync();
            }
        }

        /// <summary>
        /// Creates the form templates for the various years.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task CreateFormTemplatesAsync()
        {
            var years = new int[] { 2019 };

            var formsDataManager = _serviceProvider.GetRequiredService<IFormsDataManager>();
            await formsDataManager.CreateFormTemplatesForJurisdictionAsync(_jurisdiction.Id, ModuleKeyConsts.EconomicSubstanceBahamas, years);
        }
    }
}
