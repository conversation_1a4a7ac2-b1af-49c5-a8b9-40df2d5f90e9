using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Nevis
{
    /// <summary>
    /// Represents corporate business information in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class CorporateBusiness
    {
        /// <summary>
        /// Gets or sets a value indicating whether the company is incorporated.
        /// </summary>
        [BsonElement("incorporate")]
        public bool Incorporate { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the company is a non-tax resident.
        /// </summary>
        [BsonElement("nonTaxResident")]
        public bool? NonTaxResident { get; set; }

        /// <summary>
        /// Gets or sets the country of incorporation.
        /// </summary>
        [BsonElement("country")]
        public string Country { get; set; }
    }
}
