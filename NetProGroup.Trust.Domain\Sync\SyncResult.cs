// <copyright file="SyncResult.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

namespace NetProGroup.Trust.Domain.Sync
{
    /// <summary>
    /// Represents the result of a sync operation.
    /// </summary>
    public class SyncResult
    {
        /// <summary>
        /// Gets or sets the number of records updated.
        /// </summary>
        public int UpdatedCount { get; set; }

        /// <summary>
        /// Gets or sets the number of records deleted.
        /// </summary>
        public int DeletedCount { get; set; }
    }
}