﻿using ClosedXML.Excel;

namespace NetProGroup.Trust.DataMigration.Logging
{
    public class LogWorkbook : IDisposable
    {
        private readonly XLWorkbook _xlWorkbook;
        private bool _disposed = false;
        private const int ColumnOffset = 3;

        /// <summary>
        /// Constructor to recreate the XLWorkbook from a memorystream.
        /// </summary>
        /// <param name="memoryStream">Memory stream to read the workbook from.</param>
        private LogWorkbook(MemoryStream memoryStream)
        {
            _xlWorkbook = new XLWorkbook(memoryStream);
        }

        public LogWorkbook()
        {
            _xlWorkbook = new XLWorkbook();
        }

        private void SetHeaders(IXLWorksheet worksheet, dynamic record)
        {
            var properties = record.GetType().GetProperties();
            var dateHeaderCell = worksheet.Cell(1, 1);
            dateHeaderCell.Value = "Timestamp";
            var successHeaderCell = worksheet.Cell(1, 2);
            successHeaderCell.Value = "Success";
            
            for (int i = 0; i < properties.Length; i++)
            {
                worksheet.Cell(1, i + ColumnOffset).Value = properties[i].Name;
            }
        }

        public void AddMigratedEntity(string entityTypeName, dynamic entityIdentifier, bool success)
        {
            IXLWorksheet workSheet = GetWorksheet(entityTypeName, entityIdentifier);

            var row = workSheet.LastRowUsed().RowNumber() + 1;

            var dateCell = workSheet.Cell(row, 1);
            dateCell.Value = DateTime.UtcNow;
            dateCell.Style.DateFormat.Format = "yyyy-MM-dd HH:mm:ss";
            var successCell = workSheet.Cell(row, 2);
            successCell.Value = success;
            
            var properties = entityIdentifier.GetType().GetProperties();
            for (int i = 0; i < properties.Length; i++)
            {
                var property = properties[i];
                var propertyValue = property.GetValue(entityIdentifier);
                workSheet.Cell(row, i + ColumnOffset).Value = propertyValue ?? "<null>";
            }
        }

        public MemoryStream AsMemoryStream()
        {
            using var ms = new MemoryStream();
            if(_xlWorkbook.Worksheets.Count == 0)
            {
                _xlWorkbook.Worksheets.Add("TBD");
            }
            _xlWorkbook.SaveAs(ms);
            ms.Position = 0;

            return ms;
        }

        public string AsBase64()
        {
            using (var ms = new MemoryStream())
            {
                // THis is throwing an error on the second save
                _xlWorkbook.SaveAs(ms);
                ms.Position = 0;

                var msBytes = ms.ToArray();
                return Convert.ToBase64String(msBytes);
            }
        }

        public static LogWorkbook FromBase64(string data)
        {
            var bytes = Convert.FromBase64String(data);

            // Create a MemoryStream from the byte array
            var ms = new MemoryStream(bytes);
            ms.Position = 0;

            return new LogWorkbook(ms);
        }

        private IXLWorksheet GetWorksheet(string entityTypeName, dynamic record)
        {
            var worksheetName = $"{entityTypeName}";

            if (!_xlWorkbook.Worksheets.TryGetWorksheet(worksheetName, out var worksheet))
            {
                worksheet = _xlWorkbook.Worksheets.Add(worksheetName);
            }
            SetHeaders(worksheet, @record);

            return worksheet;
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _xlWorkbook.Dispose();
                }

                _disposed = true;
            }
        }

        void IDisposable.Dispose()
        {
            // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
            Dispose(disposing: true);
            GC.SuppressFinalize(this);
        }
    }
}
