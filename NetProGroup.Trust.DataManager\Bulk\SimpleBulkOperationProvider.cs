// <copyright file="SimpleBulkOperationProvider.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.EntityFrameworkCore;

namespace NetProGroup.Trust.DataManager.Bulk
{
    /// <summary>
    /// A simple implementation of bulk operations that uses regular EF Core operations.
    /// This is primarily intended for testing scenarios where the EFCore.BulkExtensions
    /// might not be compatible with the test database provider.
    /// </summary>
    public class SimpleBulkOperationProvider : IBulkOperationProvider
    {
        /// <inheritdoc/>
        public async Task BulkInsertAsync<T>(IEnumerable<T> entities, DbContext context, int? batchSize = null, bool? includeGraph = false) where T : class
        {
            ArgumentNullException.ThrowIfNull(entities, nameof(entities));
            ArgumentNullException.ThrowIfNull(context, nameof(context));

            var dbSet = context.Set<T>();

            foreach (var entity in entities)
            {
                await dbSet.AddAsync(entity);
            }

            await context.SaveChangesAsync();
        }

        /// <inheritdoc/>
        public async Task BulkUpdateAsync<T>(IEnumerable<T> entities, DbContext context, int? batchSize = null, bool? includeGraph = false) where T : class
        {
            ArgumentNullException.ThrowIfNull(entities, nameof(entities));
            ArgumentNullException.ThrowIfNull(context, nameof(context));

            var dbSet = context.Set<T>();

            foreach (var entity in entities)
            {
                dbSet.Update(entity);
            }

            await context.SaveChangesAsync();
        }

        /// <inheritdoc/>
        public async Task BulkDeleteAsync<T>(IEnumerable<T> entities, DbContext context, int? batchSize = null) where T : class
        {
            ArgumentNullException.ThrowIfNull(entities, nameof(entities));
            ArgumentNullException.ThrowIfNull(context, nameof(context));

            var dbSet = context.Set<T>();

            foreach (var entity in entities)
            {
                dbSet.Remove(entity);
            }

            await context.SaveChangesAsync();
        }

        /// <inheritdoc/>
        public Task<int> BulkSaveChangesAsync(DbContext context)
        {
            ArgumentNullException.ThrowIfNull(context, nameof(context));

            return context.SaveChangesAsync();
        }
    }
}
