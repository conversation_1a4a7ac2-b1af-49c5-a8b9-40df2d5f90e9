﻿// <copyright file="LegalEntitiesDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using DocumentFormat.OpenXml.Math;
using EFCore.BulkExtensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Extensions;
using NetProGroup.Framework.Services.ActivityLogs.EFModels;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Models;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Bulk;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.LegalEntities.Models;
using NetProGroup.Trust.DataManager.LegalEntities.RequestResponses;
using NetProGroup.Trust.DataManager.Modules;
using NetProGroup.Trust.DataManager.Modules.RequestResponses;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Repository.Extensions;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Domain.Shared.Enums;
using NetProGroup.Trust.Domain.Sync;
using NetProGroup.Trust.Shared.Jurisdictions;
using System.Diagnostics;
using System.Linq.Expressions;
using X.PagedList;

namespace NetProGroup.Trust.DataManager.LegalEntities
{
    /// <summary>
    /// Manager for LegalEntities data.
    /// </summary>
    public class LegalEntitiesDataManager : ILegalEntitiesDataManager
    {
        private readonly ILogger _logger;
        private readonly ILegalEntitiesRepository _legalEntityRepository;
        private readonly ILegalEntityHistoryRepository _legalEntityHistoryRepository;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly IMapper _mapper;
        private readonly IMasterClientsRepository _masterClientsRepository;
        private readonly IModulesDataManager _modulesDataManager;
        private readonly ISystemAuditManager _systemAuditManager;
        private readonly ILockManager _lockManager;
        private readonly ISyncExcludedLegalEntitiesRepository _syncExcludedLegalEntitiesRepository;
        private readonly IBulkOperationProvider _bulkOperationProvider;

        private Dictionary<string, MasterClient> _masterClients = new(StringComparer.OrdinalIgnoreCase);
        private LockDTO _jobLock;

        private List<LegalEntity> _legalEntitiesToInsert = new List<LegalEntity>();
        private Dictionary<Guid, LegalEntity> _legalEntitiesToUpdate = new Dictionary<Guid, LegalEntity>();
        private List<LegalEntityHistory> _legalEntityHistoryToInsert = new List<LegalEntityHistory>();
        private List<ActivityLog> _activityLogToInsert = new List<ActivityLog>();
        private List<SyncMessage> _syncMessagesToInsert = new List<SyncMessage>();
        private IAuthorizationFilterExpressionFactory _authorizationFilterExpressionFactory;

        /// <summary>
        /// Initializes a new instance of the <see cref="LegalEntitiesDataManager"/> class.
        /// </summary>
        /// <param name="logger">Logger instance.</param>
        /// <param name="mapper">Mapper instance.</param>
        /// <param name="repository">Instance of the repository.</param>
        /// <param name="legalEntityHistoryRepository">Instance of the legal entity history repository.</param>
        /// <param name="jurisdictionsRepository">Instance of the Jurisdiction repository.</param>
        /// <param name="masterClientsRepository">Instance of the MasterClient repository.</param>
        /// <param name="systemAuditManager">Instance of the system audit manager.</param>
        /// <param name="modulesDataManager">Instance of the modules data manager.</param>
        /// <param name="lockManager">Instance of the loks manager.</param>
        /// <param name="syncExcludedLegalEntitiesRepository">Instance of the sync excluded legal entities repository.</param>
        /// <param name="authorizationFilterExpressionFactory">Instance of the authorization filter expression factory.</param>
        /// <param name="bulkOperationProvider">Provider for bulk operations.</param>
        public LegalEntitiesDataManager(ILogger<LegalEntitiesDataManager> logger,
                                        IMapper mapper,
                                        ILegalEntitiesRepository repository,
                                        ILegalEntityHistoryRepository legalEntityHistoryRepository,
                                        IJurisdictionsRepository jurisdictionsRepository,
                                        IMasterClientsRepository masterClientsRepository,
                                        ISystemAuditManager systemAuditManager,
                                        IModulesDataManager modulesDataManager,
                                        ILockManager lockManager,
                                        ISyncExcludedLegalEntitiesRepository syncExcludedLegalEntitiesRepository,
                                        IAuthorizationFilterExpressionFactory authorizationFilterExpressionFactory,
                                        IBulkOperationProvider bulkOperationProvider)
        {
            _logger = logger;
            _mapper = mapper;

            _legalEntityRepository = repository;
            _legalEntityHistoryRepository = legalEntityHistoryRepository;
            _jurisdictionsRepository = jurisdictionsRepository;
            _masterClientsRepository = masterClientsRepository;
            _systemAuditManager = systemAuditManager;
            _modulesDataManager = modulesDataManager;
            _lockManager = lockManager;
            _syncExcludedLegalEntitiesRepository = syncExcludedLegalEntitiesRepository;
            _authorizationFilterExpressionFactory = authorizationFilterExpressionFactory;
            _bulkOperationProvider = Check.NotNull(bulkOperationProvider, nameof(bulkOperationProvider));
        }

        /// <inheritdoc/>
        public async Task<CompanyDTO> CreateCompanyAsync(CreateCompanyDTO model, bool saveChanges = false)
        {
            ArgumentNullException.ThrowIfNull(model);

            Check.NotDefaultOrNull<Guid>(model.MasterClientId, nameof(model.MasterClientId));
            Check.NotDefaultOrNull<Guid>(model.JurisdictionId, nameof(model.JurisdictionId));

            #region Setup entity

            var entity = _mapper.Map<LegalEntity>(model);

            if (model.IsActive == false)
            {
                entity.SetInactive();
            }

            #endregion

            var masterClient = await _masterClientsRepository.CheckMasterClientByIdAsync(entity.MasterClientId);
            await _systemAuditManager.AddLegalEntityAddedToMasterClientActivityLogAsync(entity, masterClient, saveChanges: false);

            await _legalEntityRepository.InsertAsync(entity, saveChanges);

            return _mapper.Map<CompanyDTO>(entity);
        }

        /// <inheritdoc/>
        public async Task<ListCompaniesResponse> ListCompaniesAsync(ListCompaniesRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var result = new ListCompaniesResponse();

            var predicate = GetLegalEntityPredicate(request);

            var pagedData = await _legalEntityRepository.FindByConditionAsPagedListAsync(predicate,
                pageNumber: request.PageNumber,
                pageSize: request.PageSize,
                options: q => ApplySorting(q, request.ToSortingInfo())
                              .Include(c => c.MasterClient)
                              .Include(c => c.Jurisdiction)
                              .TagWithCallSite());

            // Find ActivityLogs for 'CompanyDeclined'
            var legalEntityIds = pagedData.ToList().Select(le => le.Id);
            var declinedActivityLogs = (await _systemAuditManager.ListActivityLogsAsync(legalEntityIds, new List<string> { ActivityLogActivityTypes.CompanyDeclined })).ToDictionary(al => al.EntityId);

            // Set the flag for companies that have a declined entry in ActivityLogs
            var subset = _mapper.Map<List<CompanyDTO>>(pagedData);

            foreach (var company in subset)
            {
                if (declinedActivityLogs.ContainsKey(company.Id))
                {
                    company.PreviouslyDeclined = true;
                }
            }

            result.CompanyItems = new StaticPagedList<CompanyDTO>(subset, pagedData.GetMetaData());

            return result;
        }

        /// <inheritdoc/>
        public async Task<SearchCompaniesResponse> SearchCompaniesAsync(SearchCompaniesRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            // Check access to MasterClient by user
            var hasAccess = await _masterClientsRepository.AnyByConditionAsync(x => x.Id == request.MasterClientId && x.MasterClientUsers.Any(mcu => mcu.UserId == request.UserId));
            if (!hasAccess)
            {
                throw new ForbiddenException(ApplicationErrors.MASTERCLIENT_USER_NOT_FOUND.ToErrorCode(), "User has no access to masterclient");
            }

            var result = new SearchCompaniesResponse();

            var predicate = GetLegalEntityPredicate(request);

            var data = await _legalEntityRepository.FindByConditionAsync(predicate,
                                                                         options: q => q.OrderBy(c => c.Name)
                                                                                        .Include(c => c.MasterClient)
                                                                                        .Include(c => c.Jurisdiction));

            foreach (var item in data.ToList())
            {
                result.CompanyItems.Add(new CompaniesSearchResultDTO
                {
                    CompanyId = item.Id,
                    CompanyName = item.Name,
                    IncorporationNumber = item.IncorporationNr,
                    IsActive = item.IsActive,
                    JurisdictionId = item.JurisdictionId.GetValueOrDefault(),
                    JurisdictionName = item.Jurisdiction?.Name,
                    MasterClientId = item.MasterClientId,
                    MasterClientCode = item.MasterClient?.Code,
                    EntityType = item.EntityTypeName,
                    VPEntityStatus = item.EntityStatus
                });
            }

            return result;
        }

        /// <inheritdoc/>
        public async Task<SearchCompaniesWithAnnualFeeStatusResponse> SearchCompaniesWithAnnualFeeStatusAsync(SearchCompanyWithAnnualFeeStatusRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            const string settingKey = SettingTypes.FirsSubmissionYear;

            Expression<Func<LegalEntity, bool>> predicate = le =>
                le.EntityType == DomainShared.Enums.LegalEntityType.Company;

            predicate = predicate.And(_authorizationFilterExpressionFactory.GetLegalEntityJurisdictionFilterPredicate(request));

            // Search for companies with the search term
            predicate = predicate.And(le =>
                (string.IsNullOrEmpty(request.SearchTerm) ||
                 le.Name.Contains(request.SearchTerm) ||
                 le.Code.Contains(request.SearchTerm) ||
                 le.LegacyCode.Contains(request.SearchTerm) ||
                 le.MasterClientCode.Contains(request.SearchTerm) ||
                 le.MasterClient.Code.Contains(request.SearchTerm)));

            predicate = predicate.And(le =>

                // Only show entities with first-submission-year setting that's before the requested financial year
                ((le.Settings.Any(s => settingKey == s.Key) &&
                  Convert.ToInt32(le.Settings.Single(s => settingKey == s.Key).Value) <= request.FinancialYear)

                 // Or without the setting but with an incorporation date before the requested financial year
                 || (le.Settings.All(s => settingKey != s.Key) && le.IncorporationDate.GetValueOrDefault().Year <= request.FinancialYear)));

            // Filter by paid status, no Annual Fee record counts as not paid
            predicate = predicate.And(le => le.AnnualFees.Any(af => af.FinancialYear == request.FinancialYear && af.IsPaid) == request.IsPaid);

            var data = await _legalEntityRepository.FindByConditionAsync(predicate,
                options: q => q
                    .OrderBy(le => le.Name)
                    .Include(le => le.MasterClient)
                    .Include(le => le.Submissions)
                    .Include(le => le.AnnualFees));

            var itemsPaged = new PagedList<LegalEntity>(data, request.PageNumber, request.PageSize);

            var dtos = itemsPaged.Select(item =>
            {
                var submissionForYear = item.Submissions.SingleOrDefault(s => s.FinancialYear == request.FinancialYear);

                return new CompanyWithAnnualFeeStatusSearchResultDTO
                {
                    CompanyId = item.Id,
                    CompanyName = item.Name,
                    MasterClientId = item.MasterClientId,
                    MasterClientCode = item.MasterClient?.Code,
                    CompanyLegacyCode = item.LegacyCode,
                    CompanyCode = item.Code,
                    DateSubmissionCreated = submissionForYear?.CreatedAt,
                    DateSubmissionSubmitted = submissionForYear?.SubmittedAt,
                    IsPaid = item.AnnualFees.Any(af => af.FinancialYear == request.FinancialYear && af.IsPaid)
                };
            });

            var dtosPaged = new StaticPagedList<CompanyWithAnnualFeeStatusSearchResultDTO>(dtos, itemsPaged.GetMetaData());

            return new SearchCompaniesWithAnnualFeeStatusResponse { CompanyItems = dtosPaged };
        }

        /// <inheritdoc/>
        public async Task UpdateCompaniesAnnualFeeStatusAsync(List<Guid> companyIds, int financialYear, bool isPaid, List<Guid> authorizedJurisdictionIDs)
        {
            ArgumentNullException.ThrowIfNull(companyIds);
            ArgumentNullException.ThrowIfNull(authorizedJurisdictionIDs);

            var companies = (await _legalEntityRepository.FindByConditionAsync(
                le => companyIds.Contains(le.Id),
                entities => entities.Include(le => le.AnnualFees))).ToList();

            if (companies.Count != companyIds.Count)
            {
                var missingIds = companyIds.Except(companies.Select(c => c.Id));
                throw new EntityNotFoundException(nameof(LegalEntity), string.Join(", ", missingIds));
            }

            foreach (var company in companies)
            {
                if (!authorizedJurisdictionIDs.Contains(company.JurisdictionId.GetValueOrDefault()))
                {
                    throw new ForbiddenException(ApplicationErrors.NO_PERMISSION.ToErrorCode(), "User has no access to jurisdiction");
                }

                company.UpdateAnnualFeeStatus(financialYear, isPaid);
            }

            await _legalEntityRepository.UpdateAsync(companies, true);
        }

        /// <inheritdoc />
        public async Task<CompanyAnnualFeesDTO> GetCompanyAnnualFeeStatusesAsync(Guid companyId)
        {
            var legalEntity = await _legalEntityRepository.CheckLegalEntityByIdAsync(companyId,
                                                                                     options: (q) => q.Include(le => le.AnnualFees)
                                                                                                      .Include(le => le.Jurisdiction));

            var result = new CompanyAnnualFeesDTO
            {
                CompanyId = companyId
            };

            foreach (var annualFee in legalEntity.AnnualFees)
            {
                result.AnnualFees.Add(new AnnualFeeDTO { FinancialYear = annualFee.FinancialYear, IsPaid = annualFee.IsPaid });
            }

            var allYears = legalEntity.Jurisdiction.GetAnnualFeeYears();

            foreach (var year in allYears)
            {
                if (!result.AnnualFees.Any(x => x.FinancialYear == year))
                {
                    result.AnnualFees.Add(new AnnualFeeDTO { FinancialYear = year, IsPaid = false });
                }
            }

            result.AnnualFees = result.AnnualFees.OrderBy(af => af.FinancialYear).ToList();

            return result;
        }

        /// <inheritdoc />
        public async Task SetCompanyAnnualFeeStatusAsync(Guid companyId, List<AnnualFeeDTO> annualFees)
        {
            ArgumentNullException.ThrowIfNull(annualFees, nameof(annualFees));

            var legalEntity = await _legalEntityRepository.CheckLegalEntityByIdAsync(companyId,
                                                                                     options: (q) => q.Include(le => le.AnnualFees)
                                                                                                      .Include(le => le.Jurisdiction));

            var allYears = legalEntity.Jurisdiction.GetAnnualFeeYears();

            foreach (var annualFee in annualFees)
            {
                if (!allYears.Contains(annualFee.FinancialYear))
                {
                    throw new BadRequestException(ApplicationErrors.LEGALENTITY_ANNUALFEE_YEAR_NOT_ALLOWED.ToErrorCode(), "Year {0} is not allowed for annual fee for jurisdiction {1}", annualFee.FinancialYear, legalEntity.Jurisdiction.Name);
                }

                if (legalEntity.AnnualFeePaid(annualFee.FinancialYear) == annualFee.IsPaid)
                {
                    continue;
                }

                var annualFeeStatus = legalEntity.UpdateAnnualFeeStatus(annualFee.FinancialYear, annualFee.IsPaid);

                await _systemAuditManager.AddAnnualFeeStatusUpdatedActivityLogAsync(annualFeeStatus, legalEntity, saveChanges: false);
            }

            await _legalEntityRepository.SaveChangesAsync();
        }

        /// <inheritdoc/>
        public async Task SyncLegalEntitiesAsync(SyncLegalEntitiesRequest request, LockDTO jobLock = null, Func<DbContext, Task> beforeCommitAsync = null)
        {
            ArgumentNullException.ThrowIfNull(request);

            _logger.LogInformation("Found {CountChanged} changed entities", request.LegalEntities.Count);

            _jobLock = jobLock;

            var legalEntitiesByJurisdictionCode = new Dictionary<string, List<SyncLegalEntity>>(StringComparer.OrdinalIgnoreCase);
            var jurisdictions = new Dictionary<string, Jurisdiction>(StringComparer.OrdinalIgnoreCase);

            var jurisdictionCodes = request.LegalEntities.Select(le => le.JurisdictionCode).Distinct().ToList();

            var legalEntitiesToSkip = await _syncExcludedLegalEntitiesRepository.GetLegalEntitiesExcludedFromSyncAsync(request.LegalEntities.Select(entity => entity.Code));
            var legalEntityCodesToSkip = legalEntitiesToSkip.Select(entity => entity.Code).ToArray();
            _logger.LogInformation("Found {Count} entities to skip", legalEntityCodesToSkip.Length);

            // Check if we know all jurisdictions mentioned in the data and group the data by jurisdiction
            foreach (var jurisdictionCode in jurisdictionCodes)
            {
                var jurisdiction = await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(j => j.Code == jurisdictionCode);
                if (jurisdiction == null)
                {
                    throw new BadRequestException(ApplicationErrors.JURISDICTION_NOT_FOUND.ToErrorCode(), $"Jurisdiction with code '{jurisdictionCode}' not found");
                }

                jurisdictions[jurisdictionCode] = jurisdiction;
                legalEntitiesByJurisdictionCode[jurisdictionCode] = request.LegalEntities.Where(le =>
                    le.JurisdictionCode.Equals(jurisdictionCode, StringComparison.OrdinalIgnoreCase)
                    && !legalEntityCodesToSkip.Contains(le.Code, StringComparer.OrdinalIgnoreCase)).ToList();
            }

            ClearBulkData();

            foreach (var jurisdictionCode in jurisdictionCodes)
            {
                _logger.LogInformation("SyncLegalEntitiesAsync() for jurisdiction {Jurisdiction}", jurisdictionCode);
                await SyncLegalEntitiesAsync(jurisdictions[jurisdictionCode], legalEntitiesByJurisdictionCode[jurisdictionCode]);
            }

            using var transaction = await _legalEntityRepository.DbContext.Database.BeginTransactionAsync();

            try
            {
                if (_legalEntityRepository.DbContext.Database.IsRelational())
                {
                    _legalEntityRepository.DbContext.Database.SetCommandTimeout(300);
                }

                _logger.LogInformation("Start saving entity data (in a Tx)...");

                if (_legalEntitiesToInsert.Count > 0)
                {
                    _logger.LogInformation("Starting BulkInsert for {Count} entities...", _legalEntitiesToInsert.Count);
                    await _bulkOperationProvider.BulkInsertAsync(_legalEntitiesToInsert, _legalEntityRepository.DbContext);
                    _logger.LogInformation("BulkInsert finished");
                }
                else
                {
                    _logger.LogInformation("No entities to insert");
                }

                if (_legalEntitiesToUpdate.Count > 0)
                {
                    _logger.LogInformation("Starting BulkUpdate for {Count} entities...", _legalEntitiesToUpdate.Count);
                    await _bulkOperationProvider.BulkUpdateAsync(_legalEntitiesToUpdate.Values, _legalEntityRepository.DbContext);
                    _logger.LogInformation("BulkUpdate finished");
                }
                else
                {
                    _logger.LogInformation("No entities to update");
                }

                if (_legalEntityHistoryToInsert.Count > 0)
                {
                    _logger.LogInformation("Starting BulkInsert for {Count} entityhistory...", _legalEntityHistoryToInsert.Count);
                    await _bulkOperationProvider.BulkInsertAsync(_legalEntityHistoryToInsert, _legalEntityRepository.DbContext);
                    _logger.LogInformation("BulkInsert finished");
                }
                else
                {
                    _logger.LogInformation("No entityhistory to insert");
                }

                if (_activityLogToInsert.Count > 0)
                {
                    _logger.LogInformation("Starting BulkInsert for {Count} activitylogs...", _activityLogToInsert.Count);
                    await _bulkOperationProvider.BulkInsertAsync(_activityLogToInsert, _legalEntityRepository.DbContext);
                    _logger.LogInformation("BulkInsert finished");
                }
                else
                {
                    _logger.LogInformation("No activitylogs to insert");
                }

                if (_syncMessagesToInsert.Count > 0)
                {
                    _logger.LogInformation("Starting BulkInsert for {Count} messages...", _syncMessagesToInsert.Count);
                    await _bulkOperationProvider.BulkInsertAsync(_syncMessagesToInsert, _legalEntityRepository.DbContext);
                    _logger.LogInformation("BulkInsert finished");
                }
                else
                {
                    _logger.LogInformation("No messages to insert");
                }

                _logger.LogInformation("Save changes");
                await _legalEntityRepository.SaveChangesAsync();

                if (beforeCommitAsync != null)
                {
                    await beforeCommitAsync(_legalEntityRepository.DbContext);
                }

                await transaction.CommitAsync();

                _logger.LogInformation("Done saving data");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error saving data (Tx rolled back)");
                throw;
            }
            finally
            {
                ClearBulkData();
            }
        }

        private void ClearBulkData()
        {
            _legalEntitiesToInsert.Clear();
            _legalEntitiesToUpdate.Clear();
            _legalEntityHistoryToInsert.Clear();
            _activityLogToInsert.Clear();
        }

        /// <inheritdoc />
        public async Task<CompanyDTO> GetCompanyByIdAsync(Guid companyId)
        {
            var entity = await _legalEntityRepository.CheckLegalEntityByIdAsync(companyId,
                options: q => q.Include(le => le.MasterClient)
                               .Include(le => le.Jurisdiction));

            var result = _mapper.Map<CompanyDTO>(entity);

            var declinedActivityLogs = (await _systemAuditManager.ListActivityLogsAsync(new List<Guid> { companyId }, new List<string> { ActivityLogActivityTypes.CompanyDeclined })).ToDictionary(al => al.EntityId);
            result.PreviouslyDeclined = declinedActivityLogs.Count > 0;

            return result;
        }

        /// <inheritdoc/>
        public async Task ApproveCompanyAsync(Guid companyId)
        {
            var legalEntity = await _legalEntityRepository.CheckLegalEntityByIdAsync(companyId);

            // Verify all modules are approved
            var legalEntityModules = await _modulesDataManager.GetModulesAsync(new ListModulesRequest() { CompanyId = companyId });
            var allModulesApproved = legalEntityModules.CompanyModuleItems.All(m => m.IsApproved);
            if (!allModulesApproved)
            {
                throw new PreconditionFailedException(ApplicationErrors.LEGALENTITY_MODULES_NOT_APPROVED.ToErrorCode(),
                    "All modules must be approved before the company can be approved");
            }

            legalEntity.ApproveOnboarding();

            await _legalEntityRepository.UpdateAsync(legalEntity, saveChanges: false);

            await _systemAuditManager.AddActivityLogAsync(
                legalEntity,
                ActivityLogActivityTypes.CompanyApproved,
                "Company onboarding approved",
                saveChanges: true);
        }

        /// <inheritdoc/>
        public async Task DeclineCompanyAsync(Guid companyId, string declineReason)
        {
            var legalEntity = await _legalEntityRepository.CheckLegalEntityByIdAsync(companyId);

            legalEntity.DeclineOnboarding();

            await _legalEntityRepository.UpdateAsync(legalEntity, saveChanges: false);

            await _systemAuditManager.AddActivityLogAsync(
                legalEntity,
                ActivityLogActivityTypes.CompanyDeclined,
                "Company onboarding declined",
                $"Company onboarding has been declined. Reason: {declineReason}",
                saveChanges: true);
        }

        /// <inheritdoc />
        public async Task<List<LegalEntity>> GetNevisCompaniesWithSubmissions()
        {
            var nevisJurisdiction = (await _jurisdictionsRepository.FindByConditionAsync(j => j.Code == JurisdictionCodes.Nevis)).Single();

            var companies = (await _legalEntityRepository.FindByConditionAsync(
                le => le.JurisdictionId == nevisJurisdiction.Id,
                q => q.Include(le => le.Submissions)
                      .Include(le => le.MasterClient.MasterClientUsers).ThenInclude(mcu => mcu.User)
                      .OrderBy(le => le.CreatedAt)
                      .AsSplitQuery())).ToList();

            return companies;
        }

        /// <inheritdoc />
        public async Task<List<LegalEntity>> GetNevisLegalEntitiesWithMasterClientUsersAsync()
        {
            var nevisJurisdiction = (await _jurisdictionsRepository.FindByConditionAsync(j => j.Code == JurisdictionCodes.Nevis)).Single();

            var companies = (await _legalEntityRepository.FindByConditionAsync(
                le => le.JurisdictionId == nevisJurisdiction.Id && le.IsActive,
                q => q.Include(le => le.MasterClient.MasterClientUsers)
                      .ThenInclude(mcu => mcu.User)
                      .ThenInclude(u => u.ApplicationUserRoles)
                      .AsSplitQuery())).ToList();

            return companies;
        }

        /// <inheritdoc />
        public async Task<IEnumerable<LegalEntity>> GetNevisLegalEntitiesForSubmissionNotPaidReportAsync()
        {
            var nevisJurisdiction = (await _jurisdictionsRepository.FindByConditionAsync(j => j.Code == JurisdictionCodes.Nevis)).Single();
            var query = await _legalEntityRepository.FindByConditionAsync(
                le => le.JurisdictionId == nevisJurisdiction.Id && le.Submissions.Count() > 0,
                q => q
                    .Include(s => s.Submissions)
                        .ThenInclude(s => s.Invoice)
                            .ThenInclude(s => s.PaymentInvoices)
                                .ThenInclude(pi => pi.Payment)
                    .Include(l => l.MasterClient)
                        .ThenInclude(mc => mc.MasterClientUsers)
                            .ThenInclude(mcu => mcu.User)
                    .Include(l => l.Submissions)
                        .ThenInclude(s => s.Attributes)
                    .AsNoTracking()
                    .AsSplitQuery()
                    .TagWithCallSite());

            return query.ToList();
        }

        /// <inheritdoc />
        public async Task<IEnumerable<LegalEntity>> GetBahamasLegalEntitiesForSubmissionNotPaidReportAsync()
        {
            var bahmasJurisdiction = (await _jurisdictionsRepository.FindByConditionAsync(j => j.Code == JurisdictionCodes.Bahamas)).Single();

            var query = await _legalEntityRepository.FindByConditionAsync(
                    le => le.JurisdictionId == bahmasJurisdiction.Id,
                q => q
                    .Include(s => s.Submissions)
                    .ThenInclude(s => s.Invoice)
                    .ThenInclude(s => s.PaymentInvoices)
                    .ThenInclude(pi => pi.Payment)
                    .Include(l => l.MasterClient)
                    .ThenInclude(mc => mc.MasterClientUsers)
                    .ThenInclude(mcu => mcu.User)
                    .Include(l => l.Submissions)
                    .ThenInclude(s => s.Attributes)

                // Apply materialization
                .AsNoTracking()
                .AsSplitQuery() // Optimize large object graphs
                .TagWithCallSite());

            return query.ToList();
        }

        /// <inheritdoc />
        public async Task<CompanyAnnualFeesDTO> GetCompanyAnnualFeesAsync(Guid companyId)
        {
            var legalEntity = await _legalEntityRepository.CheckLegalEntityByIdAsync(companyId, options: (q) => q.Include(le => le.AnnualFees));

            var result = new CompanyAnnualFeesDTO
            {
                CompanyId = companyId
            };

            foreach (var annualFee in legalEntity.AnnualFees)
            {
                result.AnnualFees.Add(new AnnualFeeDTO { FinancialYear = annualFee.FinancialYear, IsPaid = annualFee.IsPaid });
            }

            var startYear = 2019;
            var endYear = DateTime.Now.Year;
            for (var year = startYear; year <= endYear; year++)
            {
                if (!result.AnnualFees.Any(x => x.FinancialYear == year))
                {
                    result.AnnualFees.Add(new AnnualFeeDTO { FinancialYear = year, IsPaid = false });
                }
            }

            result.AnnualFees = result.AnnualFees.OrderBy(af => af.FinancialYear).ToList();

            return result;
        }

        /// <inheritdoc />
        public async Task SetCompanyAnnualFeeAsync(Guid companyId, List<AnnualFeeDTO> annualFees)
        {
            ArgumentNullException.ThrowIfNull(annualFees, nameof(annualFees));

            var legalEntity = await _legalEntityRepository.CheckLegalEntityByIdAsync(companyId, options: (q) => q.Include(le => le.AnnualFees));

            foreach (var annualFee in annualFees)
            {
                var annualFeeStatus = legalEntity.UpdateAnnualFeeStatus(annualFee.FinancialYear, annualFee.IsPaid);

                await _systemAuditManager.AddAnnualFeeStatusUpdatedActivityLogAsync(annualFeeStatus, legalEntity, saveChanges: false);
            }

            await _legalEntityRepository.SaveChangesAsync();
        }

        /// <summary>
        /// Synchronizes the list of legal entities.
        /// </summary>
        /// <param name="jurisdiction">The jurisdiction to sync the legal entities for.</param>
        /// <param name="legalEntitiesSync">The list of legal entities for the jurisdiction.</param>
        /// <returns>A <see cref="Task"/>representing the result of the asynchronous operation.</returns>
        private async Task SyncLegalEntitiesAsync(Jurisdiction jurisdiction, IList<SyncLegalEntity> legalEntitiesSync)
        {
            ArgumentNullException.ThrowIfNull(legalEntitiesSync, nameof(legalEntitiesSync));
            ArgumentNullException.ThrowIfNull(jurisdiction, nameof(jurisdiction));

            var sw = new Stopwatch();
            sw.Start();

            // Read all existing entities for the jurisdiction
            var allExistingLegalEntities = await _legalEntityRepository.DbContext.Set<LegalEntity>().Where(x => x.JurisdictionId == jurisdiction.Id)
                                                                       .Select(entity => new { SubmissionsCount = entity.Submissions.Count, LegalEntity = entity }).ToListAsync();
            var existingLegalEntities = allExistingLegalEntities.ToDictionary(le => le.LegalEntity.Code, arg => arg.LegalEntity, StringComparer.OrdinalIgnoreCase);

            await CreateOrUpdateMasterClientsAsync(legalEntitiesSync);

            // Reload all master clients so we have the correct ids.
            var existingMasterClients = await _masterClientsRepository.FindAllAsync();
            _masterClients = existingMasterClients.ToDictionary(mc => mc.Code, StringComparer.OrdinalIgnoreCase);

            var countTotal = 0;
            var countCreated = 0;
            var countChanged = 0;

            var processedCodes = new HashSet<string>(StringComparer.OrdinalIgnoreCase);

            foreach (var syncLegalEntity in legalEntitiesSync)
            {
                using var logScope = _logger.BeginScope("SyncLegalEntity: {EntityCode}", syncLegalEntity.Code);
                countTotal++;

                if (processedCodes.Contains(syncLegalEntity.Code))
                {
                    _logger.LogWarning("Duplicate code {EntityCode}", syncLegalEntity.Code);
                    continue;
                }

                processedCodes.Add(syncLegalEntity.Code);

                if (syncLegalEntity.EntityType == DomainShared.Enums.LegalEntityType.Unknown)
                {
                    throw new ConstraintException($"The EntityType '{syncLegalEntity.EntityType}' is not valid");
                }

                if (_jobLock != null && sw.Elapsed.TotalSeconds > 60) // TODO should look at remaining time instead of elapsed seconds
                {
                    await _lockManager.RefreshLockAsync(_jobLock.Id.Value);
                    sw.Restart();
                }

                var masterClient = _masterClients[syncLegalEntity.MasterClientCode];

                // Check existing legal entity
                existingLegalEntities.TryGetValue(syncLegalEntity.Code, out var legalEntity);

                // Is there already a history record?
                LegalEntityHistory legalEntityHistory = null;
                if (legalEntity != null)
                {
                    legalEntityHistory = await _legalEntityHistoryRepository.GetCurrentCompanyByEntityCodeAsync(jurisdiction.Id, syncLegalEntity.Code);
                }

                if (legalEntity == null)
                {
                    // Create the entity when status = Active
                    if (StatusIsForActive(syncLegalEntity.EntityStatusCode))
                    {
                        _logger.LogDebug("Entity {EntityCode} is {NewEntityStatus}", syncLegalEntity.Code, syncLegalEntity.EntityStatus);
                        countCreated++;

                        // Create the inactive entry so we can assign the BO's and Directors already while in staging.
                        // Set to true now for testing
                        legalEntity = CreateEntry(syncLegalEntity, OnboardingStatus.Onboarding, isActive: false);
                        legalEntity.MasterClientId = masterClient.Id;
                        legalEntity.JurisdictionId = jurisdiction.Id;

                        _activityLogToInsert.Add(await _systemAuditManager.CreateLegalEntityAddedToMasterClientActivityLogAsync(legalEntity, masterClient));
                        _legalEntitiesToInsert.Add(legalEntity);

                        // Just create a history entry with initial status, the item needs to show up at the onboarding.
                        var legalEntityHistoryEntry = CreateHistoryEntry(jurisdiction.Id, syncLegalEntity, DomainShared.Enums.LegalEntityStatus.Initial);
                        _legalEntityHistoryToInsert.Add(legalEntityHistoryEntry);

                        // Add to list of existing to avoid duplicates
                        existingLegalEntities.Add(legalEntity.Code, legalEntity);
                    }
                    else if (StatusIsForInactive(syncLegalEntity.EntityStatusCode))
                    {
                        _logger.LogDebug("Entity {EntityCode} is {NewEntityStatus}", syncLegalEntity.Code, syncLegalEntity.EntityStatus);
                        countCreated++;

                        // Create the inactive entry so we can assign the BO's and Directors already while in staging.
                        // Set to true now for testing
                        legalEntity = CreateEntry(syncLegalEntity, OnboardingStatus.Unknown, isActive: false);
                        legalEntity.MasterClientId = masterClient.Id;
                        legalEntity.JurisdictionId = jurisdiction.Id;

                        _activityLogToInsert.Add(await _systemAuditManager.CreateLegalEntityAddedToMasterClientActivityLogAsync(legalEntity, masterClient));
                        _legalEntitiesToInsert.Add(legalEntity);

                        // Just create a history entry with initial status, the item needs to show up at the onboarding.
                        var legalEntityHistoryEntry = CreateHistoryEntry(jurisdiction.Id, syncLegalEntity, DomainShared.Enums.LegalEntityStatus.Initial);
                        _legalEntityHistoryToInsert.Add(legalEntityHistoryEntry);

                        // Add to list of existing to avoid duplicates
                        existingLegalEntities.Add(legalEntity.Code, legalEntity);
                    }
                }
                else if (StatusIsForInactive(syncLegalEntity.EntityStatusCode))
                {
                    _logger.LogDebug("Entity {EntityCode} is {NewEntityStatus}", syncLegalEntity.Code, syncLegalEntity.EntityStatus);

                    if (IsBasicChanged(legalEntityHistory, syncLegalEntity) ||
                        IsIncorporationChanged(legalEntityHistory, syncLegalEntity))
                    {
                        UpdateBasic(syncLegalEntity, legalEntity);
                        UpdateIncorporation(syncLegalEntity, legalEntity);
                        _legalEntitiesToUpdate.TryAdd(legalEntity.Id, legalEntity);

                        // Create a new history entry for received update
                        var legalEntityHistoryEntry = CreateHistoryEntry(jurisdiction.Id, syncLegalEntity, DomainShared.Enums.LegalEntityStatus.UpdateReceived);
                        _legalEntityHistoryToInsert.Add(legalEntityHistoryEntry);
                    }
                }
                else if (IsIncorporationChanged(legalEntityHistory, syncLegalEntity))
                {
                    countChanged++;
                    _logger.LogDebug("Incorporation changed for entity {EntityCode}", syncLegalEntity.Code);

                    // Create a new history entry for received update
                    var legalEntityHistoryEntry = CreateHistoryEntry(jurisdiction.Id, syncLegalEntity, DomainShared.Enums.LegalEntityStatus.UpdateReceived);
                    if (legalEntityHistory == null)
                    {
                        legalEntityHistoryEntry.Status = DomainShared.Enums.LegalEntityStatus.Initial;
                    }

                    _legalEntityHistoryToInsert.Add(legalEntityHistoryEntry);

                    // Update the basics of the legal entity and the incorporation
                    UpdateBasic(syncLegalEntity, legalEntity);
                    UpdateIncorporation(syncLegalEntity, legalEntity);
                    _legalEntitiesToUpdate.TryAdd(legalEntity.Id, legalEntity);
                }
                else if (IsBasicChanged(legalEntityHistory, syncLegalEntity))
                {
                    countChanged++;
                    _logger.LogDebug("Basic data changed for entity {EntityCode}", syncLegalEntity.Code);

                    // Add history entry but confirm immediately unless the legal entity is not active
                    var legalEntityHistoryEntry = CreateHistoryEntry(jurisdiction.Id, syncLegalEntity, DomainShared.Enums.LegalEntityStatus.Confirmed);
                    if (legalEntity.IsActive == false)
                    {
                        legalEntityHistoryEntry.Status = DomainShared.Enums.LegalEntityStatus.UpdateReceived;
                    }

                    _legalEntityHistoryToInsert.Add(legalEntityHistoryEntry);

                    // Update only the basics of the legal entity
                    UpdateBasic(syncLegalEntity, legalEntity);
                    _legalEntitiesToUpdate.TryAdd(legalEntity.Id, legalEntity);
                }

                // If the entity is not active but VP says yes then update the status and write the activity log
                if (legalEntity != null && legalEntity.IsActive == false && StatusIsForActive(syncLegalEntity.EntityStatusCode))
                {
                    switch (legalEntity.OnboardingStatus)
                    {
                        case OnboardingStatus.Approved:
                            {
                                if (allExistingLegalEntities.Single(arg => arg.LegalEntity.Code == legalEntity.Code).SubmissionsCount > 0)
                                {
                                    _activityLogToInsert.Add(await _systemAuditManager.CreateActivityLogAsync(legalEntity,
                                                                                                      ActivityLogActivityTypes.CompanyActivated,
                                                                                                      "Company re-activated",
                                                                                                      $"Updated 'Approved' status of company to 'Onboarding' by Sync process (status = {syncLegalEntity.EntityStatus})"));
                                    _logger.LogDebug("Company {CompanyCode} was inactive with onboardingStatus 'Approved' and has submissions, re-activating", legalEntity.Code);

                                    legalEntity.Reactivate();

                                    _legalEntitiesToUpdate.TryAdd(legalEntity.Id, legalEntity);
                                }
                                else
                                {
                                    _activityLogToInsert.Add(await _systemAuditManager.CreateActivityLogAsync(legalEntity,
                                                                                                      ActivityLogActivityTypes.CompanyOnboardingChanged,
                                                                                                      "Approved company re-activated in VP",
                                                                                                      $"Updated 'Approved' status of company to 'Onboarding' by Sync process (status = {syncLegalEntity.EntityStatus})"));
                                    _logger.LogDebug("Company {CompanyCode} was inactive with onboardingStatus 'Approved', re-activating with onboardingStatus 'Onboarding'", legalEntity.Code);

                                    legalEntity.ResetToOnboarding();

                                    _legalEntitiesToUpdate.TryAdd(legalEntity.Id, legalEntity);
                                }

                                break;
                            }

                        case OnboardingStatus.Onboarding:
                            {
                                // Ignore, keep status OnBoarding
                                _logger.LogWarning("Company {CompanyCode} is already in 'Onboarding' status, skipping re-activation", legalEntity.Code);
                                break;
                            }

                        case OnboardingStatus.ClosedWhileOnboarding:
                            {
                                _activityLogToInsert.Add(await _systemAuditManager.CreateActivityLogAsync(legalEntity,
                                                                                                  ActivityLogActivityTypes.CompanyOnboardingChanged,
                                                                                                  "Company updated from 'ClosedWhileOnboarding' back to 'Onboarding'",
                                                                                                  $"Updated 'ClosedWhileOnboarding' status of company to 'Onboarding' by Sync process (status = {syncLegalEntity.EntityStatus})"));
                                _logger.LogDebug("Company {CompanyCode} was inactive with onboardingStatus 'ClosedWhileOnboarding', re-activating with onboardingStatus 'Onboarding'", legalEntity.Code);

                                legalEntity.ResetToOnboarding();
                                _legalEntitiesToUpdate.TryAdd(legalEntity.Id, legalEntity);

                                break;
                            }

                        case OnboardingStatus.Declined:
                            {
                                _activityLogToInsert.Add(await _systemAuditManager.CreateActivityLogAsync(legalEntity,
                                                                                                  ActivityLogActivityTypes.CompanyOnboardingChanged,
                                                                                                  "Company updated from 'Declined' to 'Onboarding'",
                                                                                                  $"Updated 'Declined' status of company to 'Onboarding' by Sync process (status = {syncLegalEntity.EntityStatus})"));
                                _logger.LogDebug("Company {CompanyCode} was inactive with onboardingStatus 'Declined', re-activating with onboardingStatus 'Onboarding'", legalEntity.Code);

                                legalEntity.ResetToOnboarding();
                                _legalEntitiesToUpdate.TryAdd(legalEntity.Id, legalEntity);

                                break;
                            }
                    }
                }

                // If the status indicates closed
                else if (legalEntity != null && StatusIsForInactive(syncLegalEntity.EntityStatusCode))
                {
                    if (legalEntity.IsActive)
                    {
                        _activityLogToInsert.Add(await _systemAuditManager.CreateActivityLogAsync(legalEntity,
                                                                                                  ActivityLogActivityTypes.CompanyDeactivated,
                                                                                                  "Company de-activated",
                                                                                                  $"Company status from 'Active' to 'Inactive' by Sync process (status = {syncLegalEntity.EntityStatus})"));
                        _logger.LogDebug("De-activating company {CompanyCode} with status 'Inactive'", legalEntity.Code);

                        legalEntity.SetInactive();
                        _legalEntitiesToUpdate.TryAdd(legalEntity.Id, legalEntity);

                        await _modulesDataManager.DeactivateModulesForInactiveLegalEntityAsync(legalEntity, _activityLogToInsert);
                    }

                    if (legalEntity.OnboardingStatus == OnboardingStatus.Onboarding)
                    {
                        _activityLogToInsert.Add(await _systemAuditManager.CreateActivityLogAsync(legalEntity,
                            ActivityLogActivityTypes.CompanyOnboardingChanged,
                            "Company closed while onboarding",
                            $"Company status changed to 'ClosedWhileOnboarding' by Sync process (status = {syncLegalEntity.EntityStatus})"));
                        _logger.LogDebug("Closing company {CompanyCode} while onboarding", legalEntity.Code);

                        legalEntity.CloseWhileOnboarding();
                        _legalEntitiesToUpdate.TryAdd(legalEntity.Id, legalEntity);
                    }
                }

                // Is still linked to the same MasterClient?
                if (legalEntity != null && legalEntity.MasterClientId != masterClient.Id)
                {
                    var existingMasterClient = await _masterClientsRepository.GetByIdAsync(legalEntity.MasterClientId);

                    if (existingMasterClient == null)
                    {
                        _activityLogToInsert.Add(await _systemAuditManager.CreateActivityLogAsync(legalEntity,
                            ActivityLogActivityTypes.CompanyMasterClientChanged,
                            "MasterClient for company added",
                            $"Added Company to MasterClientCode '{syncLegalEntity.MasterClientCode}'"));
                        _logger.LogDebug("Company {CompanyCode} added to MasterClient {MasterClientCode}", legalEntity.Code, syncLegalEntity.MasterClientCode);
                    }
                    else
                    {
                        _activityLogToInsert.Add(await _systemAuditManager.CreateActivityLogAsync(legalEntity,
                            ActivityLogActivityTypes.CompanyMasterClientChanged,
                            "MasterClient for company changed",
                            $"Changed MasterClient for company from MasterClientCode '{existingMasterClient.Code}' to '{syncLegalEntity.MasterClientCode}'"));
                        _logger.LogDebug("Company {CompanyCode} changed MasterClient from {OldMasterClientCode} to {NewMasterClientCode}", legalEntity.Code, existingMasterClient.Code, syncLegalEntity.MasterClientCode);

                        _activityLogToInsert.Add(await _systemAuditManager.CreateActivityLogAsync(existingMasterClient,
                            ActivityLogActivityTypes.MasterClientLegalEntityRemoved,
                            "Company removed from MasterClient",
                            $"Company '{legalEntity.Name}' removed from MasterClient '{existingMasterClient.Code}'"));
                        _logger.LogDebug("Company {CompanyCode} removed from MasterClient {OldMasterClientCode}", legalEntity.Code, existingMasterClient.Code);
                    }

                    legalEntity.MasterClientId = masterClient.Id;
                }
            }
        }

        /// <summary>
        /// Creates or updates all masterclients from the legal entities.
        /// </summary>
        /// <param name="syncLegalEntities">Collection with legal entities to check the masterclient for.</param>
        private async Task CreateOrUpdateMasterClientsAsync(IList<SyncLegalEntity> syncLegalEntities)
        {
            // Preload all masterclients if needed
            var existingMasterClients = await _masterClientsRepository.FindAllAsync();
            _masterClients = existingMasterClients.ToDictionary(mc => mc.Code, StringComparer.OrdinalIgnoreCase);

            _logger.LogInformation("CreateOrUpdateMasterClientsAsync() starting: {Count1} existing masterclients, {Count2} entities", _masterClients.Count, syncLegalEntities.Count);

            var masterClientsToInsert = new List<MasterClient>();
            var masterClientsToUpdate = new List<MasterClient>();

            foreach (var syncLegalEntity in syncLegalEntities)
            {
                if (!_masterClients.TryGetValue(syncLegalEntity.MasterClientCode, out var value))
                {
                    var masterClient = new MasterClient()
                    {
                        Code = syncLegalEntity.MasterClientCode,
                        Name = syncLegalEntity.MasterClientName,
                        IsActive = true
                    };

                    masterClientsToInsert.Add(masterClient);
                    _masterClients.Add(masterClient.Code, masterClient);
                }
                else
                {
                    if (!value.IsActive.GetValueOrDefault())
                    {
                        value.IsActive = true;
                        masterClientsToUpdate.Add(value);
                    }
                }
            }

            using var transaction = await _masterClientsRepository.DbContext.Database.BeginTransactionAsync();

            try
            {
                if (_masterClientsRepository.DbContext.Database.IsRelational())
                {
                    _masterClientsRepository.DbContext.Database.SetCommandTimeout(300);
                }

                _logger.LogInformation("Saving masterclient data...");

                var bulkConfig = new BulkConfig { UseTempDB = true };

                if (masterClientsToInsert.Count > 0)
                {
                    _logger.LogInformation("Starting BulkInsert for {Count} masterclient...", masterClientsToInsert.Count);
                    await _bulkOperationProvider.BulkInsertAsync(masterClientsToInsert, _masterClientsRepository.DbContext);
                    _logger.LogInformation("BulkInsert finished");
                }
                else
                {
                    _logger.LogInformation("No masterclients to insert");
                }

                if (masterClientsToUpdate.Count > 0)
                {
                    _logger.LogInformation("Starting BulkUpdate for {Count} masterclient...", masterClientsToUpdate.Count);
                    await _bulkOperationProvider.BulkUpdateAsync(masterClientsToUpdate, _masterClientsRepository.DbContext);
                    _logger.LogInformation("BulkUpdate finished");
                }
                else
                {
                    _logger.LogInformation("No masterclients to update");
                }

                await transaction.CommitAsync();

                _logger.LogInformation("Done saving data");
            }
            catch (Exception ex)
            {
                await transaction.RollbackAsync();
                _logger.LogError(ex, "Error saving data (Tx rolled back)");
                throw;
            }
        }

        #region Utils

        /// <summary>
        /// Gets the predicate expression based on the provided filter criteria.
        /// </summary>
        /// <param name="request">The request with the filter criteria.</param>
        /// <returns>The predicate expression.</returns>
        private Expression<Func<LegalEntity, bool>> GetLegalEntityPredicate(ListCompaniesRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            Expression<Func<LegalEntity, bool>> predicate = le => true;

            predicate = predicate.And(le => le.EntityType == DomainShared.Enums.LegalEntityType.Company);
            predicate = predicate.And(_authorizationFilterExpressionFactory.GetLegalEntityJurisdictionFilterPredicate(request));

            if (request.MasterClientId.HasValue)
            {
                predicate = predicate.And(le => le.MasterClientId == request.MasterClientId);
            }

            if (!request.SearchTerm.IsNullOrEmpty())
            {
                predicate = predicate.And(le => le.Name.Contains(request.SearchTerm) ||
                                                         le.LegacyCode.Contains(request.SearchTerm) ||
                                                         le.Code.Contains(request.SearchTerm) ||
                                                         le.IncorporationNr.Contains(request.SearchTerm) ||
                                                         le.MasterClient.Code.Contains(request.SearchTerm) ||
                                                         le.MasterClientCode.Contains(request.SearchTerm) ||
                                                         le.Jurisdiction.Name.Contains(request.SearchTerm) ||
                                                         le.ReferralOffice.Contains(request.SearchTerm));
            }

            if (request.Active.HasValue)
            {
                predicate = predicate.And(le => le.IsActive == request.Active);
            }

            if (request.OnboardingStatuses != null && request.OnboardingStatuses.Count > 0)
            {
                if (request.OnboardingStatuses.Contains(OnboardingStatus.ClosedWhileOnboarding))
                {
                    throw new BadRequestException("OnboardingStatus ClosedWhileOnboarding not allowed");
                }

                predicate = predicate.And(le => request.OnboardingStatuses.Contains(le.OnboardingStatus));
            }
            else
            {
                predicate = predicate.And(le => le.OnboardingStatus != OnboardingStatus.ClosedWhileOnboarding);
            }

            string[] nevisAllowedTypes = [LegalEntityTypes.IBC, LegalEntityTypes.LLC];
            predicate = predicate.And(le =>
                le.Jurisdiction.Code != JurisdictionCodes.Nevis
                || nevisAllowedTypes.Contains(le.EntityTypeName));

            return predicate;
        }

        /// <summary>
        /// Gets the predicate expression based on the provided filter criteria.
        /// </summary>
        /// <param name="request">The request with the filter criteria.</param>
        /// <returns>The predicate expression.</returns>
        private static Expression<Func<LegalEntity, bool>> GetLegalEntityPredicate(SearchCompaniesRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            Expression<Func<LegalEntity, bool>> predicate = le => true;

            predicate = predicate.And(le => le.EntityType == DomainShared.Enums.LegalEntityType.Company
                                            && le.JurisdictionId != null);

            predicate = predicate.And(le => le.MasterClientId == request.MasterClientId);

            if (!request.SearchTerm.IsNullOrEmpty())
            {
                predicate = predicate.And(le => le.Name.Contains(request.SearchTerm) ||
                                                         le.LegacyCode.Contains(request.SearchTerm) ||
                                                         le.Code.Contains(request.SearchTerm) ||
                                                         le.IncorporationNr.Contains(request.SearchTerm) ||
                                                         le.MasterClient.Code.Contains(request.SearchTerm) ||
                                                         le.MasterClientCode.Contains(request.SearchTerm) ||
                                                         le.Jurisdiction.Name.Contains(request.SearchTerm));
            }

            if (request.Active.HasValue)
            {
                predicate = predicate.And(le => le.IsActive == request.Active);
            }

            if (request.OnboardingStatus.HasValue)
            {
                predicate = predicate.And(le => le.OnboardingStatus == request.OnboardingStatus);
            }

            return predicate;
        }

        /// <summary>
        /// Checks if the existing history entity is changed compared to the syncing data
        /// </summary>
        /// <param name="legalEntityHistory">The existing history entity.</param>
        /// <param name="syncLegalEntity">The syncing data.</param>
        /// <returns>True if the history entity is changed, false otherwise.</returns>
        private static bool IsBasicChanged(LegalEntityHistory legalEntityHistory, SyncLegalEntity syncLegalEntity)
        {
            if (legalEntityHistory == null)
            {
                return true;
            }

            return IsChanged(legalEntityHistory.Name, syncLegalEntity.Name.LimitLength(LegalEntityConsts.NameMaxLength)) ||
                   IsChanged(legalEntityHistory.EntityTypeCode, syncLegalEntity.EntityTypeCode) ||
                   IsChanged(legalEntityHistory.EntityTypeName, syncLegalEntity.EntityTypeName) ||
                   IsChanged(legalEntityHistory.LegacyCode, syncLegalEntity.LegacyCode) ||
                   IsChanged(legalEntityHistory.ReferralOffice, syncLegalEntity.ReferralOffice) ||
                   IsChanged(legalEntityHistory.ProductionOffice, syncLegalEntity.ProductionOffice) ||
                   IsChanged(legalEntityHistory.EntityStatus, syncLegalEntity.EntityStatus) ||
                   IsChanged(legalEntityHistory.EntityStatus, syncLegalEntity.EntityStatus) ||
                   IsChanged(legalEntityHistory.EntitySubStatus, syncLegalEntity.EntitySubStatus) ||
                   IsChanged(legalEntityHistory.RiskGroup, syncLegalEntity.RiskGroup) ||
                   IsChanged(legalEntityHistory.MasterClientCode, syncLegalEntity.MasterClientCode) ||
                   IsChanged(legalEntityHistory.Manager, syncLegalEntity.Manager) ||
                   IsChanged(legalEntityHistory.Administrator, syncLegalEntity.Administrator);
        }

        /// <summary>
        /// Checks if the existing staged entity is changed compared to the syncing data for at least one of the designated fields.
        /// </summary>
        /// <param name="legalEntityHistory">The existing history entity.</param>
        /// <param name="syncLegalEntity">The syncing data.</param>
        /// <returns>True if the history entity is changed, false otherwise.</returns>
        private static bool IsIncorporationChanged(LegalEntityHistory legalEntityHistory, SyncLegalEntity syncLegalEntity)
        {
            if (legalEntityHistory == null)
            {
                return true;
            }

            return IsChanged(legalEntityHistory.IncorporationNr, syncLegalEntity.IncorporationNr) ||
                   IsChanged(legalEntityHistory.IncorporationDate, syncLegalEntity.IncorporationDate) ||
                   IsChanged(legalEntityHistory.JurisdictionOfRegistration, syncLegalEntity.JurisdictionCode);
        }

        private static LegalEntityHistory CreateHistoryEntry(Guid jurisdictionId, SyncLegalEntity syncLegalEntity, DomainShared.Enums.LegalEntityStatus status)
        {
            var legalEntityStaging = new LegalEntityHistory
            {
                JurisdictionId = jurisdictionId,

                ExternalUniqueId = syncLegalEntity.UniqueId,

                Code = syncLegalEntity.Code,
                Name = syncLegalEntity.Name.LimitLength(LegalEntityConsts.NameMaxLength),
                EntityType = syncLegalEntity.EntityType,
                EntityTypeCode = syncLegalEntity.EntityTypeCode,
                EntityTypeName = syncLegalEntity.EntityTypeName,
                IncorporationNr = syncLegalEntity.IncorporationNr,
                IncorporationDate = syncLegalEntity.IncorporationDate,
                JurisdictionOfRegistration = syncLegalEntity.JurisdictionCode,
                LegacyCode = syncLegalEntity.LegacyCode,
                ReferralOffice = syncLegalEntity.ReferralOffice,
                ProductionOffice = syncLegalEntity.ProductionOffice,
                EntityStatus = syncLegalEntity.EntityStatus,
                EntitySubStatus = syncLegalEntity.EntitySubStatus,
                RiskGroup = syncLegalEntity.RiskGroup,
                MasterClientCode = syncLegalEntity.MasterClientCode,
                Manager = syncLegalEntity.Manager,
                Administrator = syncLegalEntity.Administrator,

                ReceivedAt = DateTime.UtcNow,

                Status = status
            };
            return legalEntityStaging;
        }

        private static LegalEntity CreateEntry(SyncLegalEntity syncLegalEntity, OnboardingStatus onboardingStatus, bool isActive = true)
        {
            var legalEntity = new LegalEntity(onboardingStatus)
            {
                ExternalUniqueId = syncLegalEntity.UniqueId,

                Code = syncLegalEntity.Code,
                LegacyCode = syncLegalEntity.LegacyCode,
                Name = syncLegalEntity.Name.LimitLength(LegalEntityConsts.NameMaxLength),
                EntityType = syncLegalEntity.EntityType,
                EntityTypeCode = syncLegalEntity.EntityTypeCode,
                EntityTypeName = syncLegalEntity.EntityTypeName,
                IncorporationNr = syncLegalEntity.IncorporationNr,
                IncorporationDate = syncLegalEntity.IncorporationDate,
                JurisdictionOfRegistration = syncLegalEntity.JurisdictionVPCode,
                ReferralOffice = syncLegalEntity.ReferralOffice,
                ProductionOffice = syncLegalEntity.ProductionOffice,
                EntityStatus = syncLegalEntity.EntityStatus,
                EntitySubStatus = syncLegalEntity.EntitySubStatus,
                RiskGroup = syncLegalEntity.RiskGroup,
                MasterClientCode = syncLegalEntity.MasterClientCode,
                Manager = syncLegalEntity.Manager,
                Administrator = syncLegalEntity.Administrator,
            };

            if (!isActive)
            {
                legalEntity.SetInactive();
            }

            return legalEntity;
        }

        private static LegalEntity UpdateBasic(SyncLegalEntity syncLegalEntity, LegalEntity target)
        {
            target.Name = syncLegalEntity.Name.LimitLength(LegalEntityConsts.NameMaxLength);
            target.Code = syncLegalEntity.Code;
            target.LegacyCode = syncLegalEntity.LegacyCode;
            target.EntityType = syncLegalEntity.EntityType;
            target.MasterClientCode = syncLegalEntity.MasterClientCode;
            target.EntityType = syncLegalEntity.EntityType;
            target.EntityTypeCode = syncLegalEntity.EntityTypeCode;
            target.EntityTypeName = syncLegalEntity.EntityTypeName;
            target.ReferralOffice = syncLegalEntity.ReferralOffice;
            target.ProductionOffice = syncLegalEntity.ProductionOffice;
            target.EntityStatus = syncLegalEntity.EntityStatus;
            target.EntitySubStatus = syncLegalEntity.EntitySubStatus;
            target.RiskGroup = syncLegalEntity.RiskGroup;

            return target;
        }

        private static LegalEntity UpdateIncorporation(SyncLegalEntity syncLegalEntity, LegalEntity target)
        {
            target.IncorporationNr = syncLegalEntity.IncorporationNr;
            target.IncorporationDate = syncLegalEntity.IncorporationDate;
            target.JurisdictionOfRegistration = syncLegalEntity.JurisdictionCode;

            return target;
        }

        private async Task<MasterClient> UpsertMasterClientAsync(SyncLegalEntity syncLegalEntity)
        {
            // Get existing masterclient
            if (!_masterClients.TryGetValue(syncLegalEntity.MasterClientCode, out MasterClient masterClient))
            {
                masterClient = await _masterClientsRepository.FindFirstOrDefaultByConditionAsync(x => x.Code == syncLegalEntity.MasterClientCode);
                if (masterClient == null)
                {
                    // Create the MasterClient
                    masterClient = new MasterClient(Guid.NewGuid())
                    {
                        Code = syncLegalEntity.MasterClientCode,
                        IsActive = true
                    };
                    await _masterClientsRepository.InsertAsync(masterClient, saveChanges: false);
                }

                _masterClients.Add(syncLegalEntity.MasterClientCode, masterClient);
            }

            return masterClient;
        }

#pragma warning disable SA1204 // Static elements should appear before instance elements
        private static bool IsChanged(string value1, string value2)
#pragma warning restore SA1204 // Static elements should appear before instance elements
        {
            if (value1 == null && value2 == null)
            {
                return false;
            }

            if (string.IsNullOrEmpty(value1) && string.IsNullOrEmpty(value2))
            {
                return false;
            }

            if (value1 == null || value2 == null)
            {
                return true;
            }

            return !value1.Equals(value2, StringComparison.Ordinal);
        }

        private static bool IsChanged(DateTime? value1, DateTime? value2)
        {
            if (!value1.HasValue && !value2.HasValue)
            {
                return false;
            }

            if (!value1.HasValue || !value2.HasValue)
            {
                return true;
            }

            return !value1.Value.Equals(value2.Value);
        }

        /// <summary>
        /// Checks if the status of the entity is for active state.
        /// </summary>
        /// <param name="statusCode">The status code.</param>
        /// <returns>True if code is for inactive.</returns>
        private static bool StatusIsForActive(string statusCode)
        {
            switch (statusCode)
            {
                case LegalEntityStatusCodes.Active:
                case LegalEntityStatusCodes.Closing:
                    return true;
                default: return false;
            }
        }

        /// <summary>
        /// Checks if the status of the entity is for inactive state.
        /// </summary>
        /// <param name="statusCode">The status code.</param>
        /// <returns>True if code is for inactive.</returns>
        private static bool StatusIsForInactive(string statusCode)
        {
            switch (statusCode)
            {
                case LegalEntityStatusCodes.Closed:
                case LegalEntityStatusCodes.MarkedForDeletion:
                    return true;
                default: return false;
            }
        }

        private static IQueryable<LegalEntity> ApplySorting(IQueryable<LegalEntity> query, SortingInfo sortingInfo)
        {
            if (sortingInfo == null)
            {
                return query;
            }

            sortingInfo = sortingInfo.Validate();

            Expression<Func<LegalEntity, object>> keySelector = sortingInfo.SortBy.ToLower() switch
            {
                "name" => le => le.Name,
                "code" => le => le.Code,
                "legacycode" => le => le.LegacyCode,
                "incorporationnr" => le => le.IncorporationNr,
                "incorporationdate" => le => le.IncorporationDate,
                "referraloffice" => le => le.ReferralOffice,
                "isactive" => le => le.IsActive,
                "onboardingstatus" => le => le.OnboardingStatus,
                "masterclientcode" => le => le.MasterClient.Code,
                "entitytype" => le => le.EntityTypeName,
                "vpentitystatus" => le => le.EntityStatus,
                _ => le => le.Name,
            };

            return sortingInfo.SortOrder == OrderByDefines.Ascending ? query.OrderBy(keySelector) : query.OrderByDescending(keySelector);
        }

        #endregion
    }
}
