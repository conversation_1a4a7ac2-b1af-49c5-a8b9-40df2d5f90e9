﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Trust.Application.Contracts.Announcements;
using NetProGroup.Trust.Domain.Announcements;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.AppServices.Announcements
{
    public class AnnouncementsServiceTests : TestBase
    {
        private IAnnouncementsAppService _announcementsAppService;
        private IAnnouncementsRepository _announcementsRepository;
        private List<Announcement> _announcements;

        [SetUp]
        public async Task SetUp()
        {
            _announcementsAppService = _server.Services.GetRequiredService<IAnnouncementsAppService>();
            _announcementsRepository = _server.Services.GetRequiredService<IAnnouncementsRepository>();
            await Seed();
        }

        private async Task Seed()
        {
            // Seed announcements
            _announcements = new List<Announcement>
            {
                new Announcement
                {
                    Subject = "Test Announcement 1",
                    EmailSubject = "Test Email Subject 1",
                    Body = "This is a test announcement body 1.",
                    SendAt = DateTime.UtcNow.AddDays(1),
                    Status = AnnouncementStatus.Scheduled
                },
                new Announcement
                {
                    Subject = "Test Announcement 2",
                    EmailSubject = "Test Email Subject 2",
                    Body = "This is a test announcement body 2.",
                    SendAt = DateTime.UtcNow.AddDays(1),
                    Status = AnnouncementStatus.Scheduled
                }
            };

            _announcements[0].Recipients.Add(new AnnouncementRecipient
            {
                RecipientId = _masterClient.Id,
                Type = nameof(MasterClient)
            });

            _announcements[1].Recipients.Add(new AnnouncementRecipient
            {
                RecipientId = JurisdictionNevisId,
                Type = nameof(Jurisdiction)
            });

            foreach (var announcement in _announcements)
            {
                await _announcementsRepository.InsertAsync(announcement, true);
            }
        }

        [Test]
        public async Task FilterAnnouncement_ShouldGetAnnoucements()
        {
            // Arrange
            var request = new FilterAnnouncementsDTO
            {
                PageNumber = 1,
                PageSize = 10
            };

            SetWorkContextUser(ManagementUser);

            // Act
            var result = await _announcementsAppService.FilterAnnouncementsAsync(request);

            // Assert
            result.Should().NotBeNull();
            result.Should().HaveCount(2);
            result[0].Body.Should().Be(_announcements[0].Body);
            result[0].Subject.Should().Be(_announcements[0].Subject);
            result[0].Status.Should().Be(_announcements[0].Status);
            result[1].Body.Should().Be(_announcements[1].Body);
            result[1].Subject.Should().Be(_announcements[1].Subject);
            result[1].Status.Should().Be(_announcements[1].Status);
        }

        [Test]
        public async Task FilterAnnouncement_ShouldThrowPermissionException_WhenUserIsClient()
        {
            await AssertFilterAnnouncementsThrowsForbidden(ClientUser);
        }

        [Test]
        public async Task FilterAnnouncement_ShouldThrowPermissionException_WhenUserHasWrongPermissions()
        {
            await AssertFilterAnnouncementsThrowsForbidden(BasicUser);
        }

        private async Task AssertFilterAnnouncementsThrowsForbidden(ApplicationUserDTO user)
        {
            // Arrange
            var request = new FilterAnnouncementsDTO
            {
                PageNumber = 1,
                PageSize = 10
            };

            SetWorkContextUser(user);

            // Act & Assert
            Func<Task> act = async () => await _announcementsAppService.FilterAnnouncementsAsync(request);
            await act.Should().ThrowAsync<ForbiddenException>();
        }

        [Test]
        public async Task CreateUpdateAnnouncement_ShouldCreateAnnouncement()
        {
            // Arrange
            var request = new CreateUpdateAnnouncementDTO
            {
                Subject = "New Announcement",
                EmailSubject = "New Email Subject",
                Body = "This is a new announcement body.",
                SendAt = DateTime.UtcNow.AddDays(1),
                IncludeAttachments = false,
                SendNow = false,
                SendToAllMasterClients = true,
                SendToAllJurisdictions = false,
                JurisdictionId = JurisdictionNevisId,
            };

            SetWorkContextUser(ManagementUser);

            // Act
            Guid result = await _announcementsAppService.CreateUpdateAnnouncementAsync(request);

            // Assert
            result.Should().NotBeEmpty();
            var announcement = await _announcementsRepository.GetByIdAsync(result);
            announcement.Should().NotBeNull();
            announcement.Subject.Should().Be(request.Subject);
            announcement.EmailSubject.Should().Be(request.EmailSubject);
            announcement.Body.Should().Be(request.Body);
            announcement.SendAt.Should().Be(request.SendAt);
            announcement.Status.Should().Be(AnnouncementStatus.Scheduled);
            announcement.Recipients.Should().HaveCount(1);
            announcement.Recipients.First().RecipientId.Should().Be(JurisdictionNevisId);
        }

        [Test]
        public async Task CreateUpdateAnnouncement_ShouldThrowPermissionException_WhenUserIsClient()
        {
            // Arrange
            var request = new CreateUpdateAnnouncementDTO
            {
                Subject = "New Announcement",
                EmailSubject = "New Email Subject",
                Body = "This is a new announcement body.",
                SendAt = DateTime.UtcNow.AddDays(1),
                IncludeAttachments = false,
                SendNow = false,
                SendToAllMasterClients = true,
                SendToAllJurisdictions = false,
                JurisdictionId = JurisdictionNevisId,
            };

            await AssertCreateAnnouncementThrowsForbidden(ClientUser, request);
        }

        [Test]
        public async Task CreateUpdateAnnouncement_ShouldThrowPermissionException_WhenUserHasWrongPermissions()
        {
            // Arrange
            var request = new CreateUpdateAnnouncementDTO
            {
                Subject = "New Announcement",
                EmailSubject = "New Email Subject",
                Body = "This is a new announcement body.",
                SendAt = DateTime.UtcNow.AddDays(1),
                IncludeAttachments = false,
                SendNow = false,
                SendToAllMasterClients = true,
                SendToAllJurisdictions = false,
                JurisdictionId = JurisdictionNevisId,
            };

            await AssertCreateAnnouncementThrowsForbidden(BasicUser, request);
        }

        private async Task AssertCreateAnnouncementThrowsForbidden(ApplicationUserDTO user, CreateUpdateAnnouncementDTO request)
        {
            // Arrange
            SetWorkContextUser(user);

            // Act & Assert
            Func<Task> act = async () => await _announcementsAppService.CreateUpdateAnnouncementAsync(request);
            await act.Should().ThrowAsync<ForbiddenException>();
        }
    }
}
