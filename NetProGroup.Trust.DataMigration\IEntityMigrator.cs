using NetProGroup.Framework.Services.Locks.Models;

namespace NetProGroup.Trust.DataMigration;

public interface IEntityMigrator
{
    /// <summary>
    /// Migrates Nevis invoice configurations.
    /// </summary>
    /// <param name="migrationRecord">The migration record for the process.</param>
    /// <param name="jobLock">The job lock.</param>
    /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
    Task MigrateEntitiesAsync(Domain.DataMigrations.DataMigration migrationRecord,
        LockDTO jobLock);
}