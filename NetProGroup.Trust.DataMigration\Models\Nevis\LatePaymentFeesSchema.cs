using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Nevis
{
    /// <summary>
    /// Represents a late payment fees schema in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class LatePaymentFeesSchema
    {
        /// <summary>
        /// Gets or sets the description of the late payment fee.
        /// </summary>
        [BsonElement("description")]
        public string Description { get; set; }

        /// <summary>
        /// Gets or sets the financial year associated with the late payment fee.
        /// </summary>
        [BsonElement("year")]
        public string Year { get; set; }

        /// <summary>
        /// Gets or sets the monetary value of the late payment fee.
        /// This represents the amount to be charged for late payment.
        /// </summary>
        [BsonElement("value")]
        public decimal Value { get; set; }
    }
}
