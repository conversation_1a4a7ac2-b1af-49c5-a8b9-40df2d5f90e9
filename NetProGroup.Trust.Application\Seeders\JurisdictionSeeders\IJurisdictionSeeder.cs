﻿// <copyright file="IJurisdictionSeeder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using NetProGroup.Framework.DependencyInjection.Interfaces;

namespace NetProGroup.Trust.Application.Seeders.JurisdictionSeeders
{
    /// <summary>
    /// Interface for seeding data for a jurisdiction.
    /// </summary>
    public interface IJurisdictionSeeder : IScopedService
    {
        /// <summary>
        /// Runs the seeder.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        Task RunAsync();
    }
}