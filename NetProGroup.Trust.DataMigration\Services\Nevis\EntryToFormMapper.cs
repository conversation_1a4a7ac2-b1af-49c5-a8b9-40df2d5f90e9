﻿using NetProGroup.Trust.Forms.Forms;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.DataMigration.Models.Nevis;

namespace NetProGroup.Trust.DataMigration.Services.Nevis
{
    /// <summary>
    /// Provides functionality to map Entry objects to KeyValueForm objects.
    /// </summary>
    public static class EntryToFormMapper
    {
        /// <summary>
        /// Maps an Entry object to a KeyValueForm object.
        /// </summary>
        /// <param name="entry">The Entry object to map from.</param>
        /// <param name="form">The KeyValueForm object to map to.</param>
        /// <param name="countryOverrides">Optional dictionary of country name overrides. Key is the original country name, value is what it should be mapped to.</param>
        /// <returns>A tuple indicating success and any errors encountered during mapping.</returns>
        public static (bool Success, List<string> Errors) MapEntryToForm(Entry entry, KeyValueForm form, Dictionary<string, string> countryOverrides = null)
        {
            var errors = new List<string>();

            Map(entry.Company, "companyCode");

            // Company Data
            var legalEntityDataKeyName = "legal-entity-data";
            Map(entry.CompanyData.Name, $"{legalEntityDataKeyName}.name");
            Map(entry.CompanyData.Address, $"{legalEntityDataKeyName}.address");
            Map(entry.CompanyData.Code, $"{legalEntityDataKeyName}.code");
            Map(entry.CompanyData.IncorporationCode, $"{legalEntityDataKeyName}.incorporationNr");
            Map(entry.CompanyData.MasterClientCode, $"{legalEntityDataKeyName}.masterClientCode");
            Map(entry.CompanyData.ReferralOffice, $"{legalEntityDataKeyName}.referralOffice");
            Map(entry.CompanyData.Amount, $"{legalEntityDataKeyName}.strSubmissionFee");
            Map(entry.CompanyData.HasLatePaymentException == true, $"{legalEntityDataKeyName}.strSubmissionLatePaymentFeeExempt");
            Map(entry.CompanyData.IsDeleted != true, $"{legalEntityDataKeyName}.isActive");

            // Address of Head Office
            const string stKittsCountryName = "Saint Kitts and Nevis";
            if (entry.Address != null)
            {
                Map(entry.Address.Address1, FormKeys.HeadOfficeAddress1);
                Map(entry.Address.Address2, FormKeys.HeadOfficeAddress2);
                Map(entry.Address.City, FormKeys.HeadOfficeCity);
                Map(entry.Address.Zip, FormKeys.HeadOfficeZipCode);
                MapCountry(entry.Address.Country, FormKeys.HeadOfficeCountry, errors);
                Map(entry.Address.IsStKitts, "address-of-head-office.isAddressInNevisDifferent");
                Map(entry.Address.CompanyClassification, "address-of-head-office.companyClassification");

                // Nevis Address
                if (entry.Address.AddressStKitts != null)
                {
                    Map(entry.Address.AddressStKitts.Address1, "address-of-head-office.nevisAddress1");
                    Map(entry.Address.AddressStKitts.Address2, "address-of-head-office.nevisAddress2");
                    Map(entry.Address.AddressStKitts.City, "address-of-head-office.nevisCity");
                    Map(entry.Address.AddressStKitts.Zip, "address-of-head-office.nevisZipCode");

                    if (string.IsNullOrEmpty(entry.Address.AddressStKitts.Country))
                    {
                        MapCountry(stKittsCountryName, FormKeys.HeadOfficeNevisCountry, errors);
                    }
                    else switch (entry.Address.AddressStKitts.Country)
                    {
                        case "No country":
                            Map("", FormKeys.HeadOfficeNevisCountry);
                            break;
                        case stKittsCountryName:
                            MapCountry(entry.Address.AddressStKitts.Country, FormKeys.HeadOfficeNevisCountry, errors);
                            break;
                        default:
                            errors.Add($"address.address_stKiss.country should be 'Saint Kitts and Nevis', but found '{entry.Address.AddressStKitts.Country}'");
                            break;
                    }
                }
            }

            // Contact Information
            if (entry.ClientCompany != null)
            {
                Map(entry.ClientCompany.Name, "contact-information.name");
                Map(entry.ClientCompany.Position, "contact-information.position");
                Map(entry.ClientCompany.Address1, "contact-information.address1");
                Map(entry.ClientCompany.Address2, "contact-information.address2");
                Map(entry.ClientCompany.City, "contact-information.city");
                Map(entry.ClientCompany.Zip, "contact-information.zipCode");
                MapCountry(entry.ClientCompany.Country, "contact-information.country", errors);
                Map(entry.ClientCompany.Telephone, "contact-information.telephone.number");
                Map("", "contact-information.telephone.countryCode");
                Map("", "contact-information.telephone.prefix");
                Map(entry.ClientCompany.Fax, "contact-information.fax.number");
                Map("", "contact-information.fax.countryCode");
                Map("", "contact-information.fax.prefix");
                Map(entry.ClientCompany.Email, "contact-information.email");
            }

            // Company Representative
            if (entry.CompanyRepresentative != null)
            {
                Map(entry.CompanyRepresentative.Name, "contact-information.companyRepresentativeName");
                Map(entry.CompanyRepresentative.Telephone, "contact-information.companyRepresentativeTelephone.number");
                Map("", "contact-information.companyRepresentativeTelephone.countryCode");
                Map("", "contact-information.companyRepresentativeTelephone.prefix");
                Map(entry.CompanyRepresentative.Fax, "contact-information.companyRepresentativeFax.number");
                Map("", "contact-information.companyRepresentativeFax.countryCode");
                Map("", "contact-information.companyRepresentativeFax.prefix");
                Map(entry.CompanyRepresentative.Email, "contact-information.companyRepresentativeEmail");
            }

            // Tax Resident
            if (entry.CorporateBusiness != null)
            {
                Map(entry.CorporateBusiness.Incorporate, "tax-resident.incorporatedBefore2019");
                if (entry.CorporateBusiness.NonTaxResident.HasValue)
                {
                    Map(entry.CorporateBusiness.NonTaxResident.Value, "tax-resident.nonTaxResident");
                }

                if (entry.CorporateBusiness.Country == "No country")
                {
                    Map("", "tax-resident.residentCountry");
                }
                else
                {
                    MapCountry(entry.CorporateBusiness.Country, "tax-resident.residentCountry", errors);
                }
            }

            // Intellectual Properties
            Map(entry.IntellectualPropertyAcquired?.IsAcquiredIntellectualProperty == true, "intellectual-properties.intellectualPropertyAcquired");

            if (entry.IntellectualPropertyAcquired?.AssetsAcquired != null)
            {
                for (int i = 0; i < entry.IntellectualPropertyAcquired.AssetsAcquired.Count; i++)
                {
                    var asset = entry.IntellectualPropertyAcquired.AssetsAcquired[i];
                    Map(asset.Description, $"intellectual-properties.assetsAcquired.{i}.description");
                    MapDate(asset.StartDate, $"intellectual-properties.assetsAcquired.{i}.acquisitionDate");
                    Map(asset.Income, $"intellectual-properties.assetsAcquired.{i}.income");
                }
            }

            // Corporate Accounting Records
            if (entry.FinancialReport != null)
            {
                if (entry.FinancialReport.HaveIncomeGenerated.HasValue)
                {
                    Map(entry.FinancialReport.HaveIncomeGenerated.Value, "corporate-accounting-records.assessableIncomeGenerated");
                }
                if (entry.FinancialReport.ActivityConditions.HasValue)
                {
                    Map(entry.FinancialReport.ActivityConditions.Value, "corporate-accounting-records.activitiesCondition");
                }
            }

            if (entry.FinancialReport?.ActivitiesReported != null)
            {
                for (int i = 0; i < entry.FinancialReport.ActivitiesReported.Count; i++)
                {
                    var activity = entry.FinancialReport.ActivitiesReported[i];
                    Map(activity.Description, $"corporate-accounting-records.accountingActivities.{i}.description");
                    Map(activity.RelatedParty == true, $"corporate-accounting-records.accountingActivities.{i}.relatedPartyIntellectualProperty");
                    Map(activity.NonRelatedIntellectual == true, $"corporate-accounting-records.accountingActivities.{i}.nonRelatedIntellectualProperty");
                    Map(activity.NonIntellectualProperty == true, $"corporate-accounting-records.accountingActivities.{i}.nonIntellectualProperty");
                    Map(activity.YearAmountIncome, $"corporate-accounting-records.accountingActivities.{i}.incomeYear");
                    Map(activity.AmountIncome, $"corporate-accounting-records.accountingActivities.{i}.income");
                }
            }

            // Corporate Multinational Enterprise
            if (entry.CorporationRelated != null)
            {
                Map(entry.CorporationRelated.IsCorporationRelated, "corporate-multinational-enterprise.isPartOfMNEGroup");
                if (entry.CorporationRelated.IsGrossTurnover.HasValue)
                {
                    Map(entry.CorporationRelated.IsGrossTurnover.Value, "corporate-multinational-enterprise.requiresCbCReport");
                }
            }

            // Finalize
            if (entry.DeclarationCertificate != null)
            {
                Map(entry.DeclarationCertificate.IsCheck1, "finalize.confirmationTrueInformation");
                Map(entry.DeclarationCertificate.IsCheck2, "finalize.confirmationUnderstand");
                Map(entry.DeclarationCertificate.IsCheck3, "finalize.confirmationAwarePerjury");
                MapDate(entry.DeclarationCertificate.Date, "finalize.dateOfSignature");
                Map(entry.DeclarationCertificate.Address1, "finalize.addressOfPersonDeclaring");
                Map(entry.DeclarationCertificate.Address2, "finalize.addressOfPersonDeclaring2");
                Map(entry.DeclarationCertificate.City, "finalize.city");
                Map(entry.DeclarationCertificate.Zip, "finalize.zipCode");
                MapCountry(entry.DeclarationCertificate.Country, "finalize.country", errors);
                Map(entry.DeclarationCertificate.Relation, "finalize.relation");
                Map(entry.DeclarationCertificate.Name, "finalize.nameOfPersonDeclaring");
                Map(entry.DeclarationCertificate.OwnBehalf, "finalize.onMyOwnBehalf");
                Map(entry.DeclarationCertificate.Officer, "finalize.asOfficer");
                Map(entry.DeclarationCertificate.Attorney, "finalize.asAttorney");
                Map(entry.DeclarationCertificate.Trustee, "finalize.asTrustee");
                Map(entry.DeclarationCertificate.EntitySpecified, "finalize.entitySpecified");
            }

            if (entry.CorporateAddress != null)
            {
                var keepRegisteredOffice = entry.CorporateAddress.KeepRegisteredOffice;
                Map(keepRegisteredOffice, "corporate-address.recordsKeptAtRegisteredOffice");

                var addressFormKey = keepRegisteredOffice ? "corporate-address.nevisAddress" : "corporate-address.recordsPlaceAddress";
                Map(entry.CorporateAddress.Address, addressFormKey);
            }

            // Business Activities
            if (entry.BusinessActivities != null)
            {
                var businessActivitiesPrefix = "business-activities.activities.";
                for (int i = 0; i < entry.BusinessActivities.Count; i++)
                {
                    var activity = entry.BusinessActivities[i];
                    MapDate(activity.From, $"{businessActivitiesPrefix}{i}.from");
                    MapDate(activity.To, $"{businessActivitiesPrefix}{i}.to");
                    Map(activity.Activity, $"{businessActivitiesPrefix}{i}.type");

                    if (MigrationConsts.ValidBusinessActivities.Contains(activity.Type))
                    {
                        Map(activity.Type, $"{businessActivitiesPrefix}{i}.activity");
                    }
                    else
                    {
                        Map("Other, please specify", $"{businessActivitiesPrefix}{i}.activity");
                        Map(activity.Type, $"{businessActivitiesPrefix}{i}.otherActivity");
                    }
                }
            }

            return (errors.Count == 0, errors);

            /// <summary>
            /// Maps a value to the form's dataset.
            /// </summary>
            /// <param name="entryValue">The value to map.</param>
            /// <param name="formKey">The key in the form's dataset.</param>
            void Map(object entryValue, string formKey)
            {
                if (entryValue != null)
                {
                    var value = entryValue.ToString()!;
                    if (entryValue is bool)
                    {
                        value = value.ToLowerInvariant();
                    }
                    form.DataSet.Add(formKey, value);
                }
            }

            /// <summary>
            /// Maps a country name to its alpha-3 code and adds it to the form's dataset.
            /// </summary>
            /// <param name="countryName">The name of the country.</param>
            /// <param name="formKey">The key in the form's dataset.</param>
            /// <param name="errorList">The list to add any mapping errors to.</param>
            void MapCountry(string countryName, string formKey, List<string> errorList)
            {
                if (!string.IsNullOrEmpty(countryName))
                {
                    bool success;
                    string alpha3Code;

                    // Apply country name overrides if provided
                    if (countryOverrides != null && countryOverrides.ContainsValue(countryName))
                    {
                        success = true;
                        alpha3Code = countryOverrides.Single(x => x.Value == countryName).Key;
                    }
                    else
                    {
                        (success, alpha3Code) = CountryCodeMapper.GetAlpha3Code(countryName);
                    }

                    if (!success)
                    {
                        errorList.Add($"Failed to map country: {countryName}");
                        form.DataSet.Add(formKey, countryName); // Use original name if mapping fails
                    }
                    else
                    {
                        form.DataSet.Add(formKey, alpha3Code);
                    }
                }
            }

            /// <summary>
            /// Maps a DateTime to a string and adds it to the form's dataset.
            /// </summary>
            /// <param name="dateTime">The DateTime to map.</param>
            /// <param name="formKey">The key in the form's dataset.</param>
            void MapDate(DateTime? dateTime, string formKey)
            {
                Map(dateTime?.ToString("u"), formKey);
            }
        }
    }
}
