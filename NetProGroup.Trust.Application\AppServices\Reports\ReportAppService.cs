using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Documents;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Report;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.DataManager.Reports;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Report.Enum;
using NetProGroup.Trust.Domain.Shared.Permissions;
using X.PagedList;

namespace NetProGroup.Trust.Application.AppServices.Reports
{
    /// <summary>
    /// ReportAppService implementation.
    /// </summary>
    public class ReportAppService : IReportAppService
    {
        private readonly IReportsDataManager _reportDataManager;
        private readonly IDocumentManager _documentManager;
        private readonly ISecurityManager _securityManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="ReportAppService"/> class.
        /// </summary>
        /// <param name="reportDataManager">The report data manager.</param>
        /// <param name="documentManager">The document manager.</param>
        /// <param name="securityManager">The security manager for handling user authorization.</param>
        public ReportAppService(IReportsDataManager reportDataManager, IDocumentManager documentManager, ISecurityManager securityManager)
        {
            _reportDataManager = reportDataManager;
            _documentManager = documentManager;
            _securityManager = securityManager;
        }

        /// <inheritdoc />
        public async Task<ReportDownloadResponseDTO> DownloadReportAsync(Guid reportId)
        {
            await _securityManager.RequireManagementUserAsync();

            var report = await _reportDataManager.CheckReportByIdAsync(reportId);
            await RequirePermissionAsync(report.Type);

            var document = await _reportDataManager.GetDocument(reportId);
            var bytes = await _documentManager.GetDocumentDataAsync(document);

            return ReportDownloadResponseDTO.Create(document.Filename, bytes);
        }

        /// <inheritdoc />
        public async Task<IPagedList<ReportDTO>> GetReportsByTypeAsync(ReportRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));
            ArgumentNullException.ThrowIfNull(request.ReportTypes, nameof(request.ReportTypes));

            var allowedReportTypes = new List<ReportType>();

            foreach (var reportType in request.ReportTypes)
            {
                if (await HasPermissionAsync(reportType))
                {
                    allowedReportTypes.Add(reportType);
                }
            }

            request.ReportTypes = allowedReportTypes;

            return await _reportDataManager.GetReportsByTypeAsync(request, DateTime.Today.AddMonths(-1));
        }

        private async Task RequirePermissionAsync(ReportType reportType)
        {
            var permission = reportType switch
            {
                ReportType.CompaniesWithoutSubmissions => WellKnownPermissionNames.STRModule_Management_Information,
                ReportType.ContactsInfo => WellKnownPermissionNames.STRModule_Management_Information,
                ReportType.SubmissionsNotPaid => WellKnownPermissionNames.STRModule_Management_Information,
                ReportType.BasicFinancialReport => WellKnownPermissionNames.BFRPanamaModule_Submissions_Export,
                ReportType.Financial => WellKnownPermissionNames.STRModule_Invoices_Export,
                _ => throw new BadRequestException(ApplicationErrors.NOT_SUPPORTED_FOR_REPORT.ToErrorCode(), $"Permissioncheck not supported for report '{reportType}'")
            };
            await _securityManager.RequireManagementPermissionAsync(permission);
        }

        private async Task<bool> HasPermissionAsync(ReportType reportType)
        {
            var permission = reportType switch
            {
                ReportType.CompaniesWithoutSubmissions => WellKnownPermissionNames.STRModule_Management_Information,
                ReportType.ContactsInfo => WellKnownPermissionNames.STRModule_Management_Information,
                ReportType.SubmissionsNotPaid => WellKnownPermissionNames.STRModule_Management_Information,
                ReportType.Financial => WellKnownPermissionNames.STRModule_Invoices_Export,
                _ => string.Empty
            };
            return await _securityManager.HasManagementPermissionAsync(permission);
        }
    }
}