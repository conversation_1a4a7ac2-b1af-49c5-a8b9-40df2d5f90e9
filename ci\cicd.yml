trigger: none # Is triggered by the version bump pipeline

variables:
  isDevelopmentBranch: $[eq(variables['Build.SourceBranch'], 'refs/heads/development')]
  isAlphaRelease: $[and(
          startsWith(variables['Build.SourceBranch'], 'refs/tags/'),
          contains(variables['Build.SourceBranch'], 'alpha')
          )]
  isStableRelease: $[and(
          startsWith(variables['Build.SourceBranch'], 'refs/tags/'),
          not(contains(variables['Build.SourceBranch'], 'alpha')),
          not(contains(variables['Build.SourceBranch'], 'beta'))
          )]

stages:
- stage: Build
  jobs:
  - template: build.yml
  - template: retention-template.yml
    parameters:
      condition: eq(variables['isStableRelease'], true)

# Development deployment
- template: deploy.yml
  parameters:
    stageName: DeployToDev
    environment: Development
    dependsOn: Build
    condition: and(succeeded(), or(eq(variables['isAlphaRelease'], true), eq(variables['isDevelopmentBranch'], true)))
    serviceConnection: 'DEV Infra Service Connection'
    serverName: 'sqlsrv-pcp-dev-eus2.database.windows.net'
    databaseName: 'sqldb-pcp-dev'
    webAppName: 'app-pcp-api-dev-eus2'
    resourceGroup: 'rg-npdev-pcp-app-eus2'
    slotName: 'dev'
    pool: TT PCP - WindowsAgents dev

# Test deployment
- template: deploy.yml
  parameters:
    stageName: DeployToTest
    environment: Testing
    dependsOn: Build
    condition: and(succeeded(), eq(variables['isStableRelease'], true))
    serviceConnection: 'DEV Infra Service Connection'
    serverName: 'sqlsrv-pcp-dev-eus2.database.windows.net'
    databaseName: 'sqldb-pcp-tst'
    webAppName: 'app-pcp-api-dev-eus2'
    resourceGroup: 'rg-npdev-pcp-app-eus2'
    slotName: 'production'
    pool: TT PCP - WindowsAgents dev

# Acceptance deployment
- template: deploy.yml
  parameters:
    stageName: DeployToAcc
    environment: UAT
    dependsOn: DeployToTest
    condition: false # currently deploying to UAT only from the releases/M1 branch, which has a different version of this pipeline
    serviceConnection: 'PRD Application Service Connection'
    serverName: 'sqlsrv-pcp-prd-weu.database.windows.net'
    databaseName: 'sqldb-pcp-acc'
    webAppName: 'app-pcp-api-prd-weu'
    resourceGroup: 'rg-ttg-pcp-app-weu'
    slotName: 'acc'
    pool: TT PCP - WindowsAgents prd
    retain: true

# Production deployment
- template: deploy.yml
  parameters:
    stageName: DeployToPrd
    environment: Production
    dependsOn: DeployToAcc
    condition: false # currently deploying to UAT only from the releases/M1 branch, which has a different version of this pipeline
    serviceConnection: 'PRD Application Service Connection'
    serverName: 'sqlsrv-pcp-prd-weu.database.windows.net'
    databaseName: 'sqldb-pcp-prd'
    webAppName: 'app-pcp-api-prd-weu'
    resourceGroup: 'rg-ttg-pcp-app-weu'
    slotName: 'production'
    pool: TT PCP - WindowsAgents prd
    retain: true
