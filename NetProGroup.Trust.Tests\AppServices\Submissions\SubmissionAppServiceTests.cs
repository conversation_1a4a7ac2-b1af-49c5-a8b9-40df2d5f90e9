﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.AppServices.Submissions
{
    [TestFixture()]
    public class SubmissionAppServiceTests : TestBase
    {
        private ISubmissionsAppService _sut;

        private Mock<ILockManager> _mock;


        // Panama legal entity fields
        private readonly string _panamaLegalEntityName = "Panama Company";
        private readonly string _panamaLegalEntityCode = "4";
        private readonly string _panamaLegalEntityIncorporationNr = "345678";
        private readonly string _panamaLegalEntityReferralOffice = "panama office";
        private readonly DateTime _panamaLegalEntityIncorporationDate = new DateTime(2000, 01, 10);
        private readonly string _panamaLegalEntityLegacyCode = "Panama-789";
        private Guid _panamaLegalEntityId; // Panama legal entity

        [SetUp]
        public async Task SetUpAsync()
        {
            await SetupTestData();
            _sut = _server.Services.GetRequiredService<ISubmissionsAppService>();
        }

        protected override void ConfigureTestServices(IServiceCollection services)
        {
            base.ConfigureTestServices(services);
            _mock = new Mock<ILockManager>();
            services.AddScoped(_ => _mock.Object);
        }

        [Test()]
        public async Task StartSubmissionAsync_Multiple_STR_Submissions_Request_ShouldReturn_PreconditionFailedException()
        {
            // Arrange
            SetWorkContextUser(ClientUser);

            // First call should succeed (lock acquired), second call should fail (lock not acquired)
            _mock.SetupSequence(m => m.AcquireLockAsync(It.IsAny<AcquireLockRequestDTO>()))
                 .ReturnsAsync(new LockDTO() { Id = Guid.NewGuid() })  // First call succeeds
                 .ReturnsAsync(new LockDTO() { Id = null });           // Second call fails

            // Start a submission for Panama (first call - should succeed)
            var startSubmissionData = new StartSubmissionDTO()
            {
                ModuleId = ModuleBfrId,
                LegalEntityId = _panamaLegalEntityId
            };
            await _sut.StartSubmissionAsync(startSubmissionData);
           
            // Act - Try to start another submission (second call - should fail)
            Func<Task> act = async () => await _sut.StartSubmissionAsync(startSubmissionData);

            // Assert
            var ex = await act.Should()
               .ThrowAsync<PreconditionFailedException>();
            ex.Which.Message.Should().Contain("There is a lock on this submission");
        }

        /// <summary>
        /// Setup testing data needed to execute the tests.
        /// </summary>
        private async Task SetupTestData()
        {
            var legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();
            var legalEntityModulesRepository = _server.Services.GetRequiredService<ILegalEntityModulesRepository>();

            // Create a legal entity for Panama
            var panamaLegalEntity = new LegalEntity
            {
                MasterClientId = _masterClient.Id,
                Name = _panamaLegalEntityName,
                Code = _panamaLegalEntityCode,
                LegacyCode = _panamaLegalEntityLegacyCode,
                JurisdictionId = JurisdictionPanamaId,
                EntityType = LegalEntityType.Company,
                IncorporationNr = _panamaLegalEntityIncorporationNr,
                ReferralOffice = _panamaLegalEntityReferralOffice,
                IncorporationDate = _panamaLegalEntityIncorporationDate
            };
            await legalEntitiesRepository.InsertAsync(panamaLegalEntity, true);
            _panamaLegalEntityId = panamaLegalEntity.Id;

            var existing = new LegalEntityModule(_panamaLegalEntityId, ModuleBfrId) { IsApproved = true, IsEnabled = true };

            await legalEntityModulesRepository.InsertAsync(existing, saveChanges: true);
        }
    }
}