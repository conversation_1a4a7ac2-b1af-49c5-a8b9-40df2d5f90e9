// <copyright file="CompanyMigrationServiceWrapper.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Jurisdictions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Services.Locks.Models;
using NetProGroup.Framework.Services.Locks;
using NetProGroup.Trust.Shared.Jurisdictions;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.DataManager.Settings;
using NetProGroup.Trust.Application.Contracts.Modules;
using NetProGroup.Trust.DataManager.Modules;
using NetProGroup.Trust.DataManager.Modules.RequestResponses;
using NetProGroup.Trust.DataMigration.Models.Nevis;

namespace NetProGroup.Trust.DataMigration.Services.Nevis
{
    /// <summary>
    /// Wrapper service for migrating company data.
    /// </summary>
    public class CompanyMigrationServiceWrapper : IEntityMigrator
    {
        private readonly ILogger<CompanyMigrationServiceWrapper> _logger;
        private readonly EntityMigrationService _entityMigrationService;
        private readonly IUserRepository _userRepository;
        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly IServiceProvider _serviceProvider;
        private readonly ILockManager _lockManager;
        private readonly ISettingsManager _settingsManager;
        private readonly IModulesDataManager _modulesDataManager;
        private Jurisdiction _jurisdiction;
        private List<ModuleDTO> _modules;

        /// <summary>
        /// Initializes a new instance of the <see cref="CompanyMigrationServiceWrapper"/> class.
        /// </summary>
        /// <param name="logger">The logger.</param>
        /// <param name="entityMigrationService">The entity migration service.</param>
        /// <param name="userRepository">The user repository.</param>
        /// <param name="jurisdictionsRepository">Instance of the JurisdictionsRepository.</param>
        /// <param name="serviceProvider">The service provider.</param>
        /// <param name="lockManager">The lock manager.</param>
        /// <param name="settingsManager">The settings manager.</param>
        /// <param name="modulesDataManager">The modules data manager.</param>
        public CompanyMigrationServiceWrapper(
            ILogger<CompanyMigrationServiceWrapper> logger,
            EntityMigrationService entityMigrationService,
            IUserRepository userRepository,
            IJurisdictionsRepository jurisdictionsRepository,
            IServiceProvider serviceProvider,
            ILockManager lockManager,
            ISettingsManager settingsManager,
            IModulesDataManager modulesDataManager)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _entityMigrationService = entityMigrationService ?? throw new ArgumentNullException(nameof(entityMigrationService));
            _userRepository = userRepository ?? throw new ArgumentNullException(nameof(userRepository));
            _jurisdictionsRepository = jurisdictionsRepository ?? throw new ArgumentNullException(nameof(jurisdictionsRepository));
            _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));
            _lockManager = lockManager ?? throw new ArgumentNullException(nameof(lockManager));
            _settingsManager = settingsManager ?? throw new ArgumentNullException(nameof(settingsManager));
            _modulesDataManager = modulesDataManager ?? throw new ArgumentNullException(nameof(modulesDataManager));
        }

        /// <summary>
        /// Migrates company data.
        /// </summary>
        /// <param name="migrationRecord">The migration record for the process.</param>
        /// <param name="jobLock">The job lock.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task MigrateEntitiesAsync(Domain.DataMigrations.DataMigration migrationRecord, LockDTO jobLock)
        {
            _jurisdiction ??=
                await _jurisdictionsRepository.FindFirstOrDefaultByConditionAsync(j => j.Code == JurisdictionCodes.Nevis);

            var user = await _userRepository.CheckUserByIdAsync(migrationRecord.StartedByUserId);
            var defaultFeeSetting = await _settingsManager.ReadSettingsForJurisdictionAsync<FeeSettingsDTO>(_jurisdiction.Id);
            _modules = (await _modulesDataManager.GetModulesAsync(new ListModulesRequest { JurisdictionId = _jurisdiction.Id })).ModuleItems;

            _logger.LogInformation("Retrieved jurisdiction {JurisdictionName}, default fee settings, and {ModuleCount} modules for migration", _jurisdiction.Name, _modules.Count);

            await _entityMigrationService.MigrateEntityAsync<Company>(migrationRecord, jobLock, MigrationConsts.Companies.CollectionName, MigrationConsts.Companies.DisplayName, async (company) =>
            {
                using var scope = _serviceProvider.CreateScope();
                var companyMigrationService = scope.ServiceProvider.GetRequiredService<CompanyMigrationService>();

                var (success, errors) = await companyMigrationService.HandleCompanyAsync(company, user, migrationRecord.Region, _jurisdiction, defaultFeeSetting, _modules);

                return (success, errors);
            }, company =>
            new {
                company.Id,
                company.Code,
                company.VpCode,
                company.Name,
                company.MasterClientCode,
                company.IncorporationCode
            });
        }
    }
}
