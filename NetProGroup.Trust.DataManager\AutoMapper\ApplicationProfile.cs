// <copyright file="ApplicationProfile.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using NetProGroup.Trust.Application.Contracts.Currencies;
using NetProGroup.Trust.Application.Contracts.Jurisdictions;
using NetProGroup.Trust.Application.Contracts.JurisdictionTaxRates;
using NetProGroup.Trust.Application.Contracts.LegalEntities;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.Currencies;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Application.Contracts.LegalEntities.Companies;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.BeneficialOwners;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Directors;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Shareholders;
using NetProGroup.Trust.Application.Contracts.Payments;
using NetProGroup.Trust.Application.Contracts.Payments.Invoices;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Trust.Application.Contracts.BoDir;
using NetProGroup.Trust.Application.Contracts.Users;
using NetProGroup.Trust.Domain.Payments;
using NetProGroup.Trust.Domain.Payments.Invoices;
using NetProGroup.Trust.Application.Contracts.Inboxes;
using NetProGroup.Trust.Application.Contracts.Payments.Request;
using NetProGroup.Trust.Application.Contracts.Payments.Response;
using NetProGroup.Trust.Domain.Inboxes;
using NetProGroup.Trust.Domain.SettingsModels;
using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.DataManager.BoDir.RequestResponses;
using NetProGroup.Trust.DataManager.Helpers;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Models.Input;
using NetProGroup.Trust.Payment.Provider.CXPay.Contracts.Models.Output;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Submissions.RequestResponses;
using NetProGroup.Trust.Domain.Announcements;
using NetProGroup.Trust.Application.Contracts.Announcements;
using NetProGroup.Trust.Domain.Shared.Settings;

namespace NetProGroup.Trust.DataManager.AutoMapper
{
    /// <summary>
    /// The default application profile for AutoMapper.
    /// </summary>
    public class ApplicationProfile : Profile
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="ApplicationProfile"/> class.
        /// </summary>
        public ApplicationProfile()
        {
            // Jurisdiction
            CreateMap<Jurisdiction, JurisdictionDTO>();
            CreateMap<JurisdictionTaxRate, JurisdictionTaxRateDTO>();

            // MasterClient
            CreateMap<MasterClient, MasterClientDTO>()
                .ForMember(dest => dest.Jurisdictions,
                    opt => opt.MapFrom(src => src.LegalEntities.Distinct().Select(le => le.Jurisdiction).Distinct()))
                .ForMember(dest => dest.MasterClientUsers,
                    opt => opt.Ignore())
                .ForMember(dest => dest.MasterClientManagers,
                    opt => opt.Ignore());

            // MasterClientUser > MasterClientUserDTO
            CreateMap<MasterClientUser, MasterClientUserDTO>()
                .ForMember(dest =>
                    dest.FirstName,
                    opt => opt.MapFrom(src => src.User.Name))
                .ForMember(dest =>
                    dest.LastName,
                    opt => opt.MapFrom(src => src.User.Surname))
                .ForMember(dest =>
                    dest.Email,
                    opt => opt.MapFrom(src => src.User.Email));

            // MasterClientUser > ListMasterClientUserDTO
            CreateMap<MasterClientUser, ListMasterClientUserDTO>()
                .ForMember(dest =>
                    dest.Id,
                    opt => opt.MapFrom(src => src.User.Id))
                .ForMember(dest =>
                    dest.FirstName,
                    opt => opt.MapFrom(src => src.User.Name))
                .ForMember(dest =>
                    dest.LastName,
                    opt => opt.MapFrom(src => src.User.Surname))
                .ForMember(dest =>
                    dest.DisplayName,
                    opt => opt.MapFrom(src => src.User.DisplayName))
                .ForMember(dest =>
                    dest.Email,
                    opt => opt.MapFrom(src => src.User.Email))
                .ForMember(dest =>
                    dest.IsRegistered,
                    opt => opt.MapFrom(src => src.User.ObjectId.HasValue))
                .ForMember(dest =>
                    dest.InvitationDetails,
                    opt => opt.Ignore());

            // LegalEntity
            CreateMap<CreateCompanyDTO, LegalEntity>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.ConcurrencyStamp, opt => opt.Ignore())
                .ForMember(dest => dest.EntityType, opt => opt.MapFrom(src => LegalEntityType.Company))
                .ForMember(dest => dest.ExternalUniqueId, opt => opt.Ignore())
                .ForMember(dest => dest.EntityTypeName, opt => opt.Ignore())
                .ForMember(dest => dest.EntityTypeCode, opt => opt.Ignore())
                .ForMember(dest => dest.JurisdictionOfRegistration, opt => opt.Ignore())
                .ForMember(dest => dest.EntityStatus, opt => opt.Ignore())
                .ForMember(dest => dest.EntitySubStatus, opt => opt.Ignore())
                .ForMember(dest => dest.RiskGroup, opt => opt.Ignore())
                .ForMember(dest => dest.Administrator, opt => opt.Ignore())
                .ForMember(dest => dest.Manager, opt => opt.Ignore())
                .ForMember(dest => dest.OnboardingStatus, opt => opt.Ignore())
                .ForMember(dest => dest.MasterClientCode, opt => opt.Ignore())
                .ForMember(dest => dest.MasterClient, opt => opt.Ignore())
                .ForMember(dest => dest.Jurisdiction, opt => opt.Ignore())
                .ForMember(dest => dest.LegalEntityModules, opt => opt.Ignore())
                .ForMember(dest => dest.Submissions, opt => opt.Ignore())
                .ForMember(dest => dest.Settings, opt => opt.Ignore())
                .ForMember(dest => dest.AnnualFees, opt => opt.Ignore())
                .ForMember(dest => dest.Directors, opt => opt.Ignore())
                .ForMember(dest => dest.BeneficialOwners, opt => opt.Ignore())
                .ForMember(dest => dest.InactiveSetAt, opt => opt.Ignore());

            CreateMap<LegalEntity, LegalEntityDTO>()
                .ForMember(dest => dest.JurisdictionName,
                    opt => opt.MapFrom(src => src.Jurisdiction == null ? null : src.Jurisdiction.Name))  // TODO when Jurisdiction is made mandatory, remove the null check
                .ForMember(dest => dest.MasterClientCode,
                    opt => opt.MapFrom(src => src.MasterClient == null ? null : src.MasterClient.Code))
                .ForMember(dest => dest.VPEntityStatus,
                    opt => opt.MapFrom(src => src.EntityStatus))
                .ForMember(dest => dest.EntityType,
                    opt => opt.MapFrom(src => src.EntityTypeName));

            // Companies
            CreateMap<LegalEntity, CompanyDTO>()
                .IncludeBase<LegalEntity, LegalEntityDTO>()
                .ForMember(dest => dest.IncorporationNumber, opt => opt.MapFrom(src => src.IncorporationNr))
                .ForMember(dest => dest.JurisdictionId, opt => opt.MapFrom(src => src.JurisdictionId.GetValueOrDefault()))
                .ForMember(dest => dest.PreviouslyDeclined, opt => opt.Ignore());

            // Currency
            CreateMap<Currency, CurrencyDTO>();
            CreateMap<CreateCurrencyDTO, Currency>()
                .ForMember(dest => dest.Id, opt => opt.Ignore())
                .ForMember(dest => dest.CreatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.UpdatedAt, opt => opt.Ignore())
                .ForMember(dest => dest.ConcurrencyStamp, opt => opt.Ignore());

            // BeneficialOwners
            CreateMap<BeneficialOwner, BeneficialOwnerDTO>()
                .ForMember(dest =>
                    dest.UniqueRelationCode,
                    opt => opt.MapFrom(src => src.ExternalUniqueId))
                .ForMember(dest =>
                    dest.CountryOfFormation,
                    opt => opt.MapFrom(src => src.CountryOfFormation))
                .ForMember(dest =>
                    dest.StockCode,
                    opt => opt.MapFrom(src => src.StockExchangeCode))
                .ForMember(dest =>
                    dest.StockExchange,
                    opt => opt.MapFrom(src => src.StockExchangeName))
                .ForMember(dest =>
                    dest.CountryCodeOfBirth,
                    opt => opt.MapFrom(src => src.CountryOfBirthCode))
                .ForMember(dest =>
                    dest.LegalEntityName,
                    opt => opt.MapFrom(src => src.LegalEntity == null ? null : src.LegalEntity.Name))
                .ForMember(dest => dest.IncorporationNumber, opt => opt.MapFrom(src => src.IncorporationNr))
                .ForMember(dest => dest.DateOfIncorporation, opt => opt.MapFrom(src => src.IncorporationDate))
                .ForMember(dest => dest.MetaData, opt => opt.Ignore());

            // BeneficialOwnerHistory
            CreateMap<BeneficialOwnerHistory, BeneficialOwnerDTO>()
                .ForMember(dest =>
                    dest.UniqueRelationCode,
                    opt => opt.MapFrom(src => src.ExternalUniqueId))
                .ForMember(dest =>
                    dest.CountryOfFormation,
                    opt => opt.MapFrom(src => src.Country))
                .ForMember(dest =>
                    dest.StockCode,
                    opt => opt.MapFrom(src => src.StockExchangeCode))
                .ForMember(dest =>
                    dest.StockExchange,
                    opt => opt.MapFrom(src => src.StockExchangeName))
                .ForMember(dest =>
                    dest.LegalEntityName,
                    opt => opt.MapFrom(src => src.LegalEntity == null ? null : src.LegalEntity.Name))
                .ForMember(dest =>
                    dest.IncorporationNumber,
                    opt => opt.MapFrom(src => src.IncorporationNr))
                .ForMember(dest =>
                    dest.DateOfIncorporation,
                    opt => opt.MapFrom(src => src.IncorporationDate))
                .ForMember(dest =>
                    dest.MetaData,
                    opt => opt.MapFrom(src => src))
                .ForMember(dest =>
                    dest.CountryCodeOfBirth,
                    opt => opt.MapFrom(src => src.CountryOfBirthCode));

            // LegalEntityRelationMetaData for BeneficialOwner
            CreateMap<BeneficialOwnerHistory, LegalEntityRelationMetaData>()
                .ForMember(dest =>
                    dest.StatusText,
                    opt => opt.MapFrom(src => src.Status.ToString()))
                .ForMember(dest =>
                    dest.UpdateRequestTypeName,
                    opt => opt.MapFrom(src => src.UpdateRequestType.HasValue ? src.UpdateRequestType.Value.ToString() : null))
                .ForMember(dest =>
                    dest.UpdateRequestedByUserName,
                    opt => opt.MapFrom(src => src.UpdateRequestedByUser == null ? null : src.UpdateRequestedByUser.GetDisplayName()))
                .ForMember(dest =>
                    dest.ConfirmedByUserName,
                    opt => opt.MapFrom(src => src.ConfirmedByUser == null ? null : src.ConfirmedByUser.GetDisplayName()))
                .ForMember(dest => dest.MissingDataFields, opt => opt.Ignore());

            // Directors
            CreateMap<Director, DirectorDTO>()
                .ForMember(dest =>
                    dest.UniqueRelationCode,
                    opt => opt.MapFrom(src => src.ExternalUniqueId))
                .ForMember(dest =>
                    dest.DirectorType,
                    opt => opt.MapFrom(src => src.RelationType))
                .ForMember(dest =>
                        dest.OfficerTypeName,
                    opt => opt.MapFrom(src => src.OfficerTypeName))
                .ForMember(dest =>
                    dest.DateOfIncorporation,
                    opt => opt.MapFrom(src => src.IncorporationDate))
                .ForMember(dest =>
                    dest.LegalEntityName,
                    opt => opt.MapFrom(src => src.LegalEntity == null ? null : src.LegalEntity.Name))
                .ForMember(dest =>
                    dest.CountryCodeOfBirth,
                    opt => opt.MapFrom(src => src.CountryOfBirthCode))
                .ForMember(dest => dest.IncorporationNumber, opt => opt.MapFrom(src => src.IncorporationNr))
                .ForMember(dest => dest.MetaData, opt => opt.Ignore());

            // DirectorHistory
            CreateMap<DirectorHistory, DirectorDTO>()
                .ForMember(dest =>
                    dest.UniqueRelationCode,
                    opt => opt.MapFrom(src => src.ExternalUniqueId))
                .ForMember(dest =>
                    dest.DirectorType,
                    opt => opt.MapFrom(src => src.RelationType))
                .ForMember(dest =>
                        dest.OfficerTypeName,
                    opt => opt.MapFrom(src => src.OfficerTypeName))
                .ForMember(dest =>
                    dest.DateOfIncorporation,
                    opt => opt.MapFrom(src => src.IncorporationDate))
                .ForMember(dest =>
                    dest.LegalEntityName,
                    opt => opt.MapFrom(src => src.LegalEntity == null ? null : src.LegalEntity.Name))
                .ForMember(dest =>
                    dest.MetaData,
                    opt => opt.MapFrom(src => src))
                .ForMember(dest =>
                    dest.IncorporationNumber,
                    opt => opt.MapFrom(src => src.IncorporationNr))
                .ForMember(dest =>
                    dest.CountryCodeOfBirth,
                    opt => opt.MapFrom(src => src.CountryOfBirthCode));

            // LegalEntityRelationMetaData for DirectorHistory
            CreateMap<DirectorHistory, LegalEntityRelationMetaData>()
                .ForMember(dest =>
                    dest.StatusText,
                    opt => opt.MapFrom(src => src.Status.ToString()))
                .ForMember(dest =>
                    dest.UpdateRequestTypeName,
                    opt => opt.MapFrom(src => src.UpdateRequestType.HasValue ? src.UpdateRequestType.Value.ToString() : null))
                .ForMember(dest =>
                    dest.UpdateRequestedByUserName,
                    opt => opt.MapFrom(src => src.UpdateRequestedByUser == null ? null : src.UpdateRequestedByUser.GetDisplayName()))
                .ForMember(dest =>
                    dest.ConfirmedByUserName,
                    opt => opt.MapFrom(src => src.ConfirmedByUser == null ? null : src.ConfirmedByUser.GetDisplayName()))
                .ForMember(dest => dest.MissingDataFields, opt => opt.Ignore());

            // Shareholders
            CreateMap<Shareholder, ShareholderDTO>()
                .ForMember(dest =>
                    dest.UniqueRelationCode,
                    opt => opt.MapFrom(src => src.ExternalUniqueId))
                .ForMember(dest =>
                    dest.LegalEntityName,
                    opt => opt.MapFrom(src => src.LegalEntity == null ? null : src.LegalEntity.Name))
                .ForMember(dest => dest.IsIndividual, opt => opt.Ignore())
                .ForMember(dest => dest.OfficerTypeCode, opt => opt.Ignore())
                .ForMember(dest => dest.OfficerTypeName, opt => opt.Ignore())
                .ForMember(dest => dest.DateOfBirth, opt => opt.Ignore())
                .ForMember(dest => dest.PlaceOfBirth, opt => opt.Ignore())
                .ForMember(dest => dest.Nationality, opt => opt.Ignore())
                .ForMember(dest => dest.ResidentialAddress, opt => opt.Ignore())
                .ForMember(dest => dest.IncorporationNumber, opt => opt.Ignore())
                .ForMember(dest => dest.DateOfIncorporation, opt => opt.Ignore())
                .ForMember(dest => dest.Address, opt => opt.Ignore())
                .ForMember(dest => dest.CountryOfFormation, opt => opt.Ignore())
                .ForMember(dest => dest.JurisdictionOfRegulator, opt => opt.Ignore())
                .ForMember(dest => dest.NameOfRegulator, opt => opt.Ignore())
                .ForMember(dest => dest.SovereignState, opt => opt.Ignore())
                .ForMember(dest => dest.TIN, opt => opt.Ignore())
                .ForMember(dest => dest.StockCode, opt => opt.Ignore())
                .ForMember(dest => dest.StockExchange, opt => opt.Ignore())
                .ForMember(dest => dest.MetaData, opt => opt.Ignore())
                .ForMember(dest => dest.CountryOfBirth, opt => opt.Ignore())
                .ForMember(dest => dest.CountryCodeOfBirth, opt => opt.Ignore());

            // ShareholderHistory
            CreateMap<ShareholderHistory, ShareholderDTO>()
                .ForMember(dest =>
                    dest.UniqueRelationCode,
                    opt => opt.MapFrom(src => src.ExternalUniqueId))
                .ForMember(dest =>
                    dest.LegalEntityName,
                    opt => opt.MapFrom(src => src.LegalEntity == null ? null : src.LegalEntity.Name))
                .ForMember(dest =>
                    dest.MetaData,
                    opt => opt.MapFrom(src => src))
                .ForMember(dest => dest.IsIndividual, opt => opt.Ignore())
                .ForMember(dest => dest.OfficerTypeCode, opt => opt.Ignore())
                .ForMember(dest => dest.OfficerTypeName, opt => opt.Ignore())
                .ForMember(dest => dest.DateOfBirth, opt => opt.Ignore())
                .ForMember(dest => dest.PlaceOfBirth, opt => opt.Ignore())
                .ForMember(dest => dest.Nationality, opt => opt.Ignore())
                .ForMember(dest => dest.ResidentialAddress, opt => opt.Ignore())
                .ForMember(dest => dest.IncorporationNumber, opt => opt.Ignore())
                .ForMember(dest => dest.DateOfIncorporation, opt => opt.Ignore())
                .ForMember(dest => dest.Address, opt => opt.Ignore())
                .ForMember(dest => dest.CountryOfFormation, opt => opt.Ignore())
                .ForMember(dest => dest.JurisdictionOfRegulator, opt => opt.Ignore())
                .ForMember(dest => dest.NameOfRegulator, opt => opt.Ignore())
                .ForMember(dest => dest.SovereignState, opt => opt.Ignore())
                .ForMember(dest => dest.TIN, opt => opt.Ignore())
                .ForMember(dest => dest.StockCode, opt => opt.Ignore())
                .ForMember(dest => dest.StockExchange, opt => opt.Ignore())
                .ForMember(dest => dest.CountryOfBirth, opt => opt.Ignore())
                .ForMember(dest => dest.CountryCodeOfBirth, opt => opt.Ignore());

            // LegalEntityRelationMetaData for Shareholder
            CreateMap<ShareholderHistory, LegalEntityRelationMetaData>()
                .ForMember(dest =>
                    dest.StatusText,
                    opt => opt.MapFrom(src => src.Status.ToString()))
                .ForMember(dest =>
                    dest.UpdateRequestTypeName,
                    opt => opt.MapFrom(src => src.UpdateRequestType.HasValue ? src.UpdateRequestType.Value.ToString() : null))
                .ForMember(dest =>
                    dest.UpdateRequestedByUserName,
                    opt => opt.MapFrom(src => src.UpdateRequestedByUser == null ? null : src.UpdateRequestedByUser.GetDisplayName()))
                .ForMember(dest =>
                    dest.ConfirmedByUserName,
                    opt => opt.MapFrom(src => src.ConfirmedByUser == null ? null : src.ConfirmedByUser.GetDisplayName()))
                .ForMember(dest =>
                    dest.MissingDataFields, opt => opt.Ignore());

            // Users
            CreateMap<ApplicationUser, ListUserDTO>()
                .ForMember(dest =>
                    dest.IsRegistered,
                    opt => opt.MapFrom(src => src.ObjectId.HasValue))
                .ForMember(dest =>
                    dest.FirstName,
                    opt => opt.MapFrom(src => src.Name))
                .ForMember(dest =>
                    dest.LastName,
                    opt => opt.MapFrom(src => src.Surname))
                .ForMember(dest =>
                    dest.InvitationDetails,
                    opt => opt.Ignore());

            // Inbox mappings
            CreateMap<InboxReadStatus, InboxReadStatusDTO>();

            // Invoices
            CreateMap<Invoice, InvoiceDTO>()
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.Amount))
                .ForMember(dest => dest.FinancialYear, opt => opt.MapFrom(src => src.FinancialYear))
                .ForMember(dest => dest.File, opt => opt.Ignore())
                .ForMember(dest => dest.CurrencySymbol, opt => opt.MapFrom(src => src.Currency.Symbol))
                .ForMember(dest => dest.PaidDate, opt => opt.MapFrom(src => src.GetPaidAt()))
                .ForMember(dest => dest.TransactionId, opt => opt.MapFrom(src => GetPaidTransaction(src).Id))
                .ForMember(dest => dest.TxId, opt => opt.MapFrom(src => GetPaidTransaction(src).TransactionId))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.GetPaymentStatus()))
                .ForMember(dest => dest.CompanyName, opt => opt.MapFrom(src => GetSubmissionFormAttributeValue(src, FormKeys.CompanyName)))
                .ForMember(dest => dest.IncorporationNr, opt => opt.MapFrom(src => GetSubmissionFormAttributeValue(src, FormKeys.CompanyIncorporationNumber)))
                .ForMember(dest => dest.Address1, opt => opt.MapFrom(src => GetSubmissionFormAttributeValue(src, FormKeys.HeadOfficeAddress1)))
                .ForMember(dest => dest.Address2, opt => opt.MapFrom(src => GetSubmissionFormAttributeValue(src, FormKeys.HeadOfficeAddress2)))
                .ForMember(dest => dest.AddressZipCode, opt => opt.MapFrom(src => GetSubmissionFormAttributeValue(src, FormKeys.HeadOfficeZipCode)))
                .ForMember(dest => dest.AddressCity, opt => opt.MapFrom(src => GetSubmissionFormAttributeValue(src, FormKeys.HeadOfficeCity)))
                .ForMember(dest => dest.AddressCountry, opt => opt.MapFrom(src => GetSubmissionFormAttributeValue(src, FormKeys.HeadOfficeCountry)));

            // InvoicesLine
            CreateMap<InvoiceLine, InvoiceLineDTO>();

            // Payments
            CreateMap<Domain.Payments.Payment, PaymentDTO>()
                .ForMember(dest => dest.CompanyName,
                    opt => opt.MapFrom(src => src.LegalEntity.Name))
                .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.Amount))
                .ForMember(dest => dest.FinancialYear,
                    opt => opt.MapFrom(src =>
                        src.PaymentInvoices.Select(pi => pi.Invoice.FinancialYear)
                            .FirstOrDefault())) // Mapping from the first associated Invoice's FinancialYear
                .ForMember(dest => dest.IncorporationNr,
                    opt => opt.MapFrom(src => src.LegalEntity.IncorporationNr))
                .ForMember(dest => dest.CurrencySymbol,
                    opt => opt.MapFrom(src => src.Currency.Symbol))
                .ForMember(dest => dest.DateTime,
                    opt => opt.MapFrom(src =>
                        src.PaymentInvoices.Select(pi => pi.Invoice.Date)
                            .FirstOrDefault())); // Assuming PaidDate is based on the first related Invoice date

            // LatePaymentFee
            CreateMap<LatePaymentFee, STRLatePaymentFeeDTO>()
                .ForMember(dest => dest.Charge, opt => opt.MapFrom(src => src.Amount));

            // InvoiceNumbering
            CreateMap<InvoiceNumberingConfiguration, InvoiceNumberingSettingsDTO>();
            CreateMap<InvoiceNumberingSettingsDTO, InvoiceNumberingConfiguration>()
                .ForMember(dest => dest.JurisdictionId, opt => opt.Ignore())
                .ForMember(dest => dest.ModuleId, opt => opt.Ignore());

            // Payment
            CreateMap<PaymentTransaction, CxPayTransactionDTO>()
            .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
            .ForMember(dest => dest.ReferenceId, opt => opt.MapFrom(src => src.PaymentId))
            .ForMember(dest => dest.Result, opt => opt.MapFrom(src => src.Result))
            .ForMember(dest => dest.ResultCode, opt => opt.MapFrom(src => src.ResultCode))
            .ForMember(dest => dest.ResultMessage, opt => opt.MapFrom(src => src.ResultMessage))
            .ForMember(dest => dest.TransactionId, opt => opt.MapFrom(src => src.TransactionId))
            .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
            .ForMember(dest => dest.CardDigits, opt => opt.MapFrom(src => src.CardDigits))
            .ForMember(dest => dest.CreatedAt, opt => opt.MapFrom(src => src.ProcessCreatedAt ?? DateTime.Now)) // Handling nullable DateTime
            .ForMember(dest => dest.PaidAt, opt => opt.MapFrom(src => src.PaidAt))
            .ForMember(dest => dest.IsFinished, opt => opt.MapFrom(src => src.IsFinished))
            .ForMember(dest => dest.FirstName, opt => opt.MapFrom(src => src.FirstName))
            .ForMember(dest => dest.LastName, opt => opt.MapFrom(src => src.LastName))
            .ForMember(dest => dest.Address, opt => opt.MapFrom(src => src.Address))
            .ForMember(dest => dest.City, opt => opt.MapFrom(src => src.City))
            .ForMember(dest => dest.State, opt => opt.MapFrom(src => src.State))
            .ForMember(dest => dest.ZipCode, opt => opt.MapFrom(src => src.ZipCode))
            .ForMember(dest => dest.Company, opt => opt.MapFrom(src => src.Company))
            .ForMember(dest => dest.PhoneNumber, opt => opt.MapFrom(src => src.PhoneNumber))
            .ForMember(dest => dest.Email, opt => opt.MapFrom(src => src.Email))
            .ForMember(dest => dest.ProviderId, opt => opt.Ignore()) // Mapping not found in PaymentTransaction
            .ForMember(dest => dest.Amount, opt => opt.MapFrom(src => src.Payment.Amount))
            .ForMember(dest => dest.LegalEntityId, opt => opt.MapFrom(src => src.Payment.LegalEntityId))
            .ForMember(dest => dest.CurrencyId, opt => opt.MapFrom(src => src.Payment.CurrencyId))
            .ForMember(dest => dest.InvoiceIds, opt => opt.MapFrom(src => src.Payment.PaymentInvoices.Select(pi => pi.InvoiceId)));

            CreateMap<Domain.Payments.Payment, PaymentDetailsResponseDTO>();
            CreateMap<PaymentTransaction, PaymentTransactionResponseDTO>();

            CreateMap<CreateTransactionRequestDTO, StartPaymentRequest>()
                .ForMember(dest => dest.Order, opt => opt.MapFrom(src => new StartPaymentRequest.OrderInfo
                {
                    Description = src.Description,
                    OrderId = src.OrderId,
                }))
                .ForMember(dest => dest.Flow,
                    opt => opt.MapFrom(src => new StartPaymentRequest.FlowInfo
                    {
                        PaymentRedirectUrl = src.PaymentRedirectUrl,
                        CancelUrl = src.CancelUrl,
                    }))
                .ForMember(dest => dest.CardHolder,
                    opt => opt.MapFrom(src => new CardholderInfo
                    {
                        CompanyName = src.CompanyName,
                        FirstName = src.FirstName,
                        LastName = src.LastName,
                        Email = src.Email,
                        PhoneNumber = src.PhoneNumber
                    }))
                .ForMember(dest => dest.Merchant, opt => opt.MapFrom(src => new MerchantInfo { Email = src.MerchantEmail }))
                .ForMember(dest => dest.PaymentId, opt => opt.MapFrom(src => src.PaymentId))
                .ForMember(dest => dest.PaymentProvider, opt => opt.Ignore())
                .ForMember(dest => dest.InvoiceIds, opt => opt.Ignore());

            // Map from CxCompletionResponse to SubmitPaymentResponseDTO
            CreateMap<CxCompletionResponse, SubmitPaymentResponseDTO>()
                .ForMember(dest => dest.TransactionId, opt => opt.MapFrom(src => src.TransactionId))
                .ForMember(dest => dest.ProviderTransactionId, opt => opt.MapFrom(src => src.ProviderTransactionId))
                .ForMember(dest => dest.Result, opt => opt.MapFrom(src => src.Result))
                .ForMember(dest => dest.ResultText, opt => opt.MapFrom(src => src.ResultText))
                .ForMember(dest => dest.ResultNumber, opt => opt.MapFrom(src => src.ResultNumber));

            CreateMap<Domain.Payments.Payment, CreatePaymentResponseDTO>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom(src => src.Id))
                .ForMember(dest => dest.CurrencyId, opt => opt.MapFrom(src => src.CurrencyId))
                .ForMember(dest => dest.DateTime, opt => opt.MapFrom(src => src.CreatedAt)) // Assuming `CreatedOn` is a timestamp for when the payment was processed
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status));

            CreateMap<Domain.Report.Report, ReportDTO>()
                .ForMember(dest => dest.Filename, opt => opt.MapFrom(src => src.Document.Filename));

            CreateMap<SearchBoDirRequestDTO, SearchBoDirRequest>()
                .ForMember(dest => dest.ProductionOffice, opt => opt.MapFrom(src => src.ProductionOffice.GetProductionOfficeEnumString()))
                .ForMember(dest => dest.AuthorizedJurisdictionIDs, opt => opt.Ignore())
                ;

            CreateMap<AnnouncementDocument, AnnouncementDocumentDTO>()
                .ForMember(dest => dest.Document, opt => opt.Ignore())
                ;

            CreateMap<Announcement, AnnouncementDTO>()
                .ForMember(dest => dest.IsSent, opt => opt.MapFrom(src => src.SentAt.HasValue ? true : false))
                .ForMember(dest => dest.MasterClientIds, opt => opt.MapFrom(src => src.Recipients.Where(ao => ao.Type == nameof(MasterClient)).Select(ao => ao.RecipientId)))
                .ForMember(dest => dest.JurisdictionIds, opt => opt.MapFrom(src => src.Recipients.Where(ao => ao.Type == nameof(Jurisdiction)).Select(ao => ao.RecipientId)))
                .ForMember(dest => dest.LegalEntityIds, opt => opt.MapFrom(src => src.Recipients.Where(ao => ao.Type == nameof(LegalEntity)).Select(ao => ao.RecipientId)))
                .ForMember(dest => dest.UserIds, opt => opt.MapFrom(src => src.Recipients.Where(ao => ao.Type == nameof(ApplicationUser)).Select(ao => ao.RecipientId)))
                ;

            CreateMap<Announcement, ListAnnouncementDTO>()
                .ForMember(dest => dest.MasterClientIds, opt => opt.MapFrom(src => src.Recipients.Where(ao => ao.Type == nameof(MasterClient)).Select(ao => ao.RecipientId)))
                .ForMember(dest => dest.JurisdictionIds, opt => opt.MapFrom(src => src.Recipients.Where(ao => ao.Type == nameof(Jurisdiction)).Select(ao => ao.RecipientId)))
                .ForMember(dest => dest.LegalEntityIds, opt => opt.MapFrom(src => src.Recipients.Where(ao => ao.Type == nameof(LegalEntity)).Select(ao => ao.RecipientId)))
                .ForMember(dest => dest.UserIds, opt => opt.MapFrom(src => src.Recipients.Where(ao => ao.Type == nameof(ApplicationUser)).Select(ao => ao.RecipientId)))
                ;
        }

        private static string GetSubmissionFormAttributeValue(Invoice invoice, string key)
        {
            // technically we support multiple submissions per invoice but currently there's always at most one.
            return invoice.Submissions.SingleOrDefault()?
                .FormDocument.Attributes.GetAttributeValue<string>(key);
        }

        #region Helpers
        #region INVOICE

        /// <summary>
        /// Gets the paid transaction for the invoice.
        /// </summary>
        /// <param name="source">The source invoice.</param>
        /// <returns>Tuple containing the transaction id and the paid at date.</returns>
        private static (Guid? Id, string TransactionId) GetPaidTransaction(Invoice source)
        {
            var transaction = source?.PaymentInvoices
                ?.FirstOrDefault()?.Payment?.PaymentTransactions
                ?.FirstOrDefault(p => p.PaidAt.HasValue);

            return transaction == null
                ? (null, null)
                : (transaction.Id, transaction.TransactionId);
        }

        #endregion
        #endregion
    }
}
