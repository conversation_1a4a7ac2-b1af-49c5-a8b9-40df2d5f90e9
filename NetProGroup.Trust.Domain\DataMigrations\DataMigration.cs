using NetProGroup.Framework.EF.Repository;
using NetProGroup.Trust.Domain.Jurisdictions;

namespace NetProGroup.Trust.Domain.DataMigrations
{
    /// <summary>
    /// Represents a data migration entity with various properties to track the migration process.
    /// </summary>
    public class DataMigration : StampedEntity<Guid>
    {
        /// <summary>
        /// Defines the possible statuses for a data migration.
        /// </summary>
        public enum MigrationStatus
        {
            NotStarted,
            InProgress,
            Completed,
            Failed,
            Cancelled
        }

        /// <summary>
        /// Initializes a new instance of the <see cref="DataMigration"/> class.
        /// </summary>
        /// <param name="jurisdictionId">The jurisdiction ID for the data migration.</param>
        /// <param name="startedByUserId">The user ID that started the migration.</param>
        public DataMigration(Guid jurisdictionId, Guid startedByUserId)
        {
            JurisdictionId = jurisdictionId;
            StartedByUserId = startedByUserId;
            Status = MigrationStatus.NotStarted;
        }

        /// <summary>
        /// Gets the region for the data migration.
        /// </summary>
        public Guid JurisdictionId { get; init; }

        /// <summary>
        /// Gets the jurisdiction for the data migration.
        /// </summary>
        public Jurisdiction Jurisdiction { get; init; }

        /// <summary>
        /// Gets the user ID of the user who started the migration.
        /// </summary>
        public Guid StartedByUserId { get; init; }

        /// <summary>
        /// Gets or sets the date and time when the migration was last updated.
        /// </summary>
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether the initial sync has been completed.
        /// </summary>
        public bool InitialSyncCompleted { get; set; }

        /// <summary>
        /// Gets a value indicating whether the migration has been completed.
        /// </summary>
        public bool MigrationCompleted { get; private set; }

        /// <summary>
        /// Gets the current status of the migration.
        /// </summary>
        public MigrationStatus Status { get; private set; }

        /// <summary>
        /// Gets or sets a value indicating whether a stop has been requested for the migration.
        /// </summary>
        public bool StopRequested { get; set; }

        /// <summary>
        /// Gets or sets a string representation of unprocessed records.
        /// </summary>
        public string UnprocessedRecords { get; set; }

        /// <summary>
        /// Gets or sets the collection of entity migration progresses.
        /// </summary>
        public ICollection<EntityMigrationProgress> EntityMigrationProgresses { get; set; } = new List<EntityMigrationProgress>();

        /// <summary>
        /// Gets the error message if the migration failed.
        /// </summary>
        public string Error { get; private set; }

        /// <summary>
        /// The jurisdiction name.
        /// </summary>
        public string Region => Jurisdiction.Name; // TODO check usages of this where it should be the code, not the name

        /// <summary>
        /// Sets the migration status to Failed and records the error message.
        /// </summary>
        /// <param name="error">The error message describing why the migration failed.</param>
        public void SetFailed(string error)
        {
            Status = MigrationStatus.Failed;
            Error = error;
            MigrationCompleted = true;
        }

        /// <summary>
        /// Sets the migration status to Completed.
        /// </summary>
        public void SetCompleted()
        {
            Status = MigrationStatus.Completed;
            MigrationCompleted = true;
        }

        /// <summary>
        /// Sets the migration status to InProgress.
        /// </summary>
        public void SetInProgress()
        {
            Status = MigrationStatus.InProgress;
        }

        /// <summary>
        /// Sets the migration status to Cancelled.
        /// </summary>
        public void SetCancelled()
        {
            Status = MigrationStatus.Cancelled;
            MigrationCompleted = true;
        }

        /// <summary>
        /// Checks if the migration status is Failed.
        /// </summary>
        /// <returns>True if the migration status is Failed, otherwise false.</returns>
        public bool IsFailed()
        {
            return Status == MigrationStatus.Failed;
        }

        /// <summary>
        /// Checks if the migration status is Cancelled.
        /// </summary>
        /// <returns>True if the migration status is Cancelled, otherwise false.</returns>
        public bool IsCancelled()
        {
            return Status == MigrationStatus.Cancelled;
        }
    }
}