﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Trust.Application.Contracts.BoDir;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.AppServices.BoDir
{
    public class BoDirAppServiceTests : TestBase
    {
        private IBoDirAppService _boDirService;
        private ILegalEntitiesRepository _legalEntitiesRepository;
        private IMasterClientsRepository _masterClientsRepository;
        private IJurisdictionsRepository _jurisdictionsRepository;
        private LegalEntity _legalEntity;

        [SetUp]
        public async Task SetUp()
        {
            _boDirService = _server.Services.GetRequiredService<IBoDirAppService>();
            _legalEntitiesRepository = _server.Services.GetRequiredService<ILegalEntitiesRepository>();
            _masterClientsRepository = _server.Services.GetRequiredService<IMasterClientsRepository>();
            _jurisdictionsRepository = _server.Services.GetRequiredService<IJurisdictionsRepository>();
            await Seed();
        }

        private async Task Seed()
        {
            MasterClient masterClient = new MasterClient(Guid.NewGuid())
            {
                Code = "TEST_123"
            };

            await _masterClientsRepository.InsertAsync(masterClient, true);
            

            _legalEntity = new LegalEntity(Guid.NewGuid())
            {
                Name = "Company-1",
                Code = "01",                
                EntityType = LegalEntityType.Company,
                MasterClient = masterClient,
                JurisdictionId = JurisdictionNevisId,
                EntityTypeName = LegalEntityTypes.IBC,
                Directors = new List<Director>
                {
                    new Director
                    {
                        Name = "Director 1",
                        Code = "D1",
                        CompanyNumber = "*********",
                        FileType = "Company",
                        RelationType = "Director",
                        DirectorHistories = new List<DirectorHistory>
                        {
                            new DirectorHistory
                            {
                                Name = "Director 1",
                                Code = "D1",
                                CompanyNumber = "*********",
                                Status = LegalEntityRelationStatus.Initial,
                                FileType = "Company",
                                RelationType = "Director",
                                CreatedAt = DateTime.UtcNow.AddDays(-5),
                            }
                        },
                    },
                    new Director
                    {
                        Name = "Director 2",
                        Code = "D2",
                        CompanyNumber = "*********",
                        FileType = "Company",
                        RelationType = "Director",
                        DirectorHistories = new List<DirectorHistory>
                        {
                            new DirectorHistory
                            {
                                Name = "Director 2",
                                Code = "D2",
                                CompanyNumber = "*********",
                                Status = LegalEntityRelationStatus.Refreshed,
                                FileType = "Company",
                                RelationType = "Director",
                                CreatedAt = DateTime.UtcNow.AddDays(-3),
                            }
                        },
                    },
                    new Director
                    {
                        Name = "Director 3",
                        Code = "D3",
                        CompanyNumber = "*********",
                        FileType = "Company",
                        RelationType = "Director",
                        DirectorHistories = new List<DirectorHistory>
                        {
                            new DirectorHistory
                            {
                                Name = "Director 3",
                                Code = "D3",
                                CompanyNumber = "*********",
                                Status = LegalEntityRelationStatus.Confirmed,
                                FileType = "Company",
                                RelationType = "Director",
                                CreatedAt = DateTime.UtcNow.AddDays(-2),
                            }
                        },
                    },
                    new Director
                    {
                        Name = "Director 4",
                        Code = "D4",
                        CompanyNumber = "*********",
                        FileType = "Company",
                        RelationType = "Director",
                        DirectorHistories = new List<DirectorHistory>
                        {
                            new DirectorHistory
                            {
                                Name = "Director 4",
                                Code = "D4",
                                CompanyNumber = "*********",
                                Status = LegalEntityRelationStatus.PendingUpdateRequest,
                                FileType = "Company",
                                RelationType = "Director",
                                CreatedAt = DateTime.UtcNow.AddDays(-1),
                            }
                        },
                    },
                    new Director
                    {
                        Name = "Director 5",
                        Code = "D5",
                        CompanyNumber = "*********",
                        FileType = "Company",
                        RelationType = "Director",
                        DirectorHistories = new List<DirectorHistory>
                        {
                            new DirectorHistory
                            {
                                Name = "Director 5",
                                Code = "D5",
                                CompanyNumber = "*********",
                                Status = LegalEntityRelationStatus.UpdateReceived,
                                FileType = "Company",
                                RelationType = "Director",
                                CreatedAt = DateTime.UtcNow.AddDays(-1),
                            }
                        },
                    }
                }
            };

            await _legalEntitiesRepository.InsertAsync(_legalEntity, true);
        }

        [Test]
        public async Task SearchBoDirsAsync_ShouldGetDirectors()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);

            // Act
            var result = await _boDirService.SearchBoDirsAsync(new SearchBoDirRequestDTO());

            // Assert
            result.Should().NotBeNull();
            result.Count.Should().BeGreaterThan(0);
            result.Count.Should().Be(5);
        }

        [Theory()]
        [TestCase(BoDirDataStatus.Initial, "D1", LegalEntityRelationStatus.Initial)]
        [TestCase(BoDirDataStatus.Refreshed, "D2", LegalEntityRelationStatus.Refreshed)]
        [TestCase(BoDirDataStatus.Confirmed, "D3", LegalEntityRelationStatus.Confirmed)]
        [TestCase(BoDirDataStatus.PendingUpdateRequest, "D4", LegalEntityRelationStatus.PendingUpdateRequest)]
        [TestCase(BoDirDataStatus.Subsequent, "D5", LegalEntityRelationStatus.UpdateReceived)]
        public async Task SearchBoDirsAsync_WithSpecificStatus_ShouldGetDirectors(BoDirDataStatus requestStatus, string expectedDirectorId, LegalEntityRelationStatus expectedResultStatus)
        {
            // Arrange
            var nevisOwner = ManagementUser;
            var workContext = _server.Services.GetRequiredService<IWorkContext>();
            workContext.IdentityUserId = nevisOwner!.Id;

            // Act
            var result = await _boDirService.SearchBoDirsAsync(new SearchBoDirRequestDTO
            {
                DataStatuses = new List<BoDirDataStatus> { requestStatus }
            });

            // Assert
            result.Should().NotBeNull();
            var director = result.Should().ContainSingle().Subject;
            director.Status.Should().Be(expectedResultStatus);
            director.DirectorVPCode.Should().Be(expectedDirectorId);
        }

        [Test]
        public async Task SearchBoDirsAsync_WithMultipleDirectorHistories_MapsCorrectStatus()
        {
            // Arrange
            SetWorkContextUser(ManagementUser);
            var directorSeed = _legalEntity.Directors.First();
            var historyData = new[]
            {
                (LegalEntityRelationStatus.Refreshed, DateTime.UtcNow.AddDays(-4)),
                (LegalEntityRelationStatus.Confirmed, DateTime.UtcNow.AddDays(-3)),
                (LegalEntityRelationStatus.UpdateReceived, DateTime.UtcNow.AddDays(-2))
            };

            foreach (var (status, createdAt) in historyData)
            {
                directorSeed.DirectorHistories.Add(new DirectorHistory
                {
                    Name = "Director 1",
                    Code = "D1",
                    CompanyNumber = "*********",
                    Status = status,
                    FileType = "Company",
                    RelationType = "Director",
                    CreatedAt = createdAt,
                });
            }


            await _legalEntitiesRepository.UpdateAsync(_legalEntity, true);

            // Act
            var result = await _boDirService.SearchBoDirsAsync(new SearchBoDirRequestDTO
            {
                DataStatuses = new List<BoDirDataStatus> { BoDirDataStatus.Subsequent },
                SearchTerm = "Director 1"
            });

            // Assert
            result.Should().NotBeNull();
            var directorResult = result.Should().ContainSingle().Subject;
            directorResult.Status.Should().Be(LegalEntityRelationStatus.UpdateReceived);
        }
    }
}
