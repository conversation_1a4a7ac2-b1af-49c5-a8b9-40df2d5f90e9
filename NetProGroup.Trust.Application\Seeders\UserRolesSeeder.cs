// <copyright file="UserRolesSeeder.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Roles;

namespace NetProGroup.Trust.Application.Seeders
{
    /// <summary>
    /// Seeder for the user roles in ProjectsDashboard.
    /// </summary>
    public static partial class UserRolesSeeder
    {
        /// <summary>
        /// Defines the seed.
        /// </summary>
        /// <param name="modelBuilder">The ModelBuilder entity.</param>
        public static void SeedUserRoles(this ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<IdentityUserRole<Guid>>().HasData
            (

                // Role 'System' for system user
                new IdentityUserRole<Guid>
                {
                    RoleId = Guid.Parse(WellKnownRoleIds.System.ToString()),
                    UserId = UserConsts.SystemUserId
                },

                // Role 'Super Admin' for system user
                new IdentityUserRole<Guid>
                {
                    RoleId = Guid.Parse("DAAAF3B2-E632-4EC8-8418-DC012239FD73"),
                    UserId = UserConsts.SystemUserId
                },

                // Role 'Nevis_Owner' for system user
                new IdentityUserRole<Guid>
                {
                    RoleId = Guid.Parse("A00D4E6C-DCD6-4FCA-B080-666EEB9AE6CE"),
                    UserId = UserConsts.SystemUserId
                });
        }
    }
}