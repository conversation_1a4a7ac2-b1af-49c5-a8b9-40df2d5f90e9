﻿using NetProGroup.Trust.Application.Contracts.Settings;
using NetProGroup.Trust.Domain.Shared.Settings;

namespace NetProGroup.Trust.Application.Contracts.Jurisdictions
{
    /// <summary>
    /// Represents settings for outputing documents.
    /// </summary>
    public class JurisdictionDocumentSettingsDTO : IAttributedSettings
    {
        /// <summary>
        /// Gets or sets the header text to use for documents.
        /// </summary>
        [SettingKey(key: DocumentKeys.Header)]
        public string Header { get; set; }

        /// <summary>
        /// Gets or sets the footer text to use for documents.
        /// </summary>
        [SettingKey(key: DocumentKeys.Footer)]
        public string Footer { get; set; }

        /// <summary>
        /// Gets or sets the companyinfo text to use for documents.
        /// </summary>
        [SettingKey(key: DocumentKeys.CompanyInfo)]
        public string CompanyInfo { get; set; }
    }
}
