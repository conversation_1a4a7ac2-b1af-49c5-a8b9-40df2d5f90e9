﻿// <copyright file="ModulesController.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.AspNetCore.Mvc;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.API.Controllers;
using NetProGroup.Trust.Application.Contracts.Modules;
using NetProGroup.Trust.Application.Contracts.Submissions;
using Swashbuckle.AspNetCore.Annotations;

namespace NetProGroup.Trust.API.Areas.Management.Controllers.Modules
{
    /// <summary>
    /// Use this controller to manage modules.
    /// </summary>
    [Area("Management")]
    [Route("api/v{version:apiVersion}/[area]/modules")]
    public class ModulesController : TrustAPIControllerBase
    {
        private readonly ILogger _logger;
        private readonly IConfiguration _configuration;

        private readonly IModulesAppService _modulesAppService;
        private readonly ISubmissionsAppService _submissionsAppService;

        /// <summary>
        /// Initializes a new instance of the <see cref="ModulesController"/> class.
        /// </summary>
        /// <param name="logger">An instance of ILogger.</param>
        /// <param name="configuration">An instance of IConfiguration.</param>
        /// <param name="modulesAppService">The service for moodules.</param>
        /// <param name="submissionsAppService">The service for submissions.</param>
        public ModulesController(
            ILogger<ModulesController> logger,
            IConfiguration configuration,
            IModulesAppService modulesAppService,
            ISubmissionsAppService submissionsAppService)
            : base(logger)
        {
            _logger = logger;
            _configuration = configuration;

            _modulesAppService = modulesAppService;
            _submissionsAppService = submissionsAppService;
        }

        /// <summary>
        /// Gets the available modules.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/modules?active=true.
        /// </remarks>
        /// <param name="active">Indicates whether to get the active (true), inactive (false) or all (omit) modules.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result with the modules.</returns>
        [HttpGet]
        [SwaggerOperation(OperationId = "GetModules")]
        [ProducesResponseType(typeof(ListModulesDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetModules(
            [FromQuery(Name = "active")] bool? active = null)
        {
            ListModulesDTO item = null;

            var result = await ProcessRequestAsync<ListModulesDTO>(
                validate: () =>
                {
                },

                executeAsync: async () =>
                {
                    item = await _modulesAppService.GetAllModulesAsync(active);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }

        /// <summary>
        /// Gets the list of years to show when searching submissions.
        /// </summary>
        /// <remarks>
        /// Sample request:
        ///
        ///     GET /api/v1/management/modules/{moduleId}/submission-years
        ///.
        /// </remarks>
        /// <param name="moduleId">The id of the module to get the allowed years for.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the action result.</returns>
        [HttpGet("{moduleId}/submission-years")]
        [SwaggerOperation(OperationId = "Management_GetAllSubmissionYears")]
        [ProducesResponseType(typeof(AllSubmissionYearsDTO), StatusCodes.Status200OK)]
        public async Task<IActionResult> GetAvailableSubmissionYears(
            Guid moduleId)
        {
            AllSubmissionYearsDTO item = null;

            var result = await ProcessRequestAsync<object>(
                validate: () =>
                {
                    Check.NotDefaultOrNull<Guid>(moduleId, nameof(moduleId));
                },
                executeAsync: async () =>
                {
                    item = await _submissionsAppService.GetAllSubmissionYears(moduleId);
                },

                createResponseModel: () =>
                {
                    return item;
                });

            return result.AsResponse();
        }
    }
}
