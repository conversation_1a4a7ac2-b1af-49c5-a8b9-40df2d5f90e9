﻿// <copyright file="SecurityManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Microsoft.Extensions.Logging;
using System.Diagnostics.CodeAnalysis;
using System.Reflection;
using NetProGroup.Framework.Caching;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Shared.Permissions;
using NetProGroup.Trust.Domain.Shared.Roles;
using NetProGroup.Trust.Domain.Submissions;

namespace NetProGroup.Trust.DataManager.Security
{
    /// <summary>
    /// Manager for security checks. Use this manager for centralized checks on roles and permissions.
    /// </summary>
    public class SecurityManager : ISecurityManager
    {
        private readonly IUserManager _userManager;
        private readonly ICacheManager _cacheManager;

        private readonly IJurisdictionsRepository _jurisdictionsRepository;
        private readonly IMasterClientsRepository _masterClientsRepository;
        private readonly IMasterClientUsersRepository _masterClientUsersRepository;
        private readonly ILegalEntitiesRepository _legalEntitiesRepository;

        private readonly Dictionary<Guid, IList<string>> _jurisdictionRoleNames = new Dictionary<Guid, IList<string>>();
        private readonly Dictionary<string, IList<string>> _permissionsWithRoles = new Dictionary<string, IList<string>>();

        private readonly ISubmissionsRepository _submissionsRepository;
        private readonly ISubmissionsIncludingDeletedRepository _submissionsIncludingDeletedRepository;
        private readonly ILogger<SecurityManager> _logger;

        private Guid? _userId;
        private Guid? _contextJurisdictionId;

        /// <summary>
        /// Initializes a new instance of the <see cref="SecurityManager"/> class.
        /// </summary>
        /// <param name="workContext">The current work context.</param>
        /// <param name="cacheManager">Manager of the 'per request' cache.</param>
        /// <param name="userManager">UserManager instance.</param>
        /// <param name="jurisdictionsRepository">Instance of jurisdiction repository.</param>
        /// <param name="masterClientsRepository">Instance of master clients repository.</param>
        /// <param name="masterClientUsersRepository">Instance of masterclientusers repository.</param>
        /// <param name="legalEntitiesRepository">Instance of legal entities repository.</param>
        /// <param name="submissionsRepository">Instance of submissions repository.</param>
        /// <param name="submissionsIncludingDeletedRepository">Instance of submissions including deleted repository.</param>
        /// <param name="logger">Instance of the logger.</param>
        public SecurityManager(IWorkContext workContext,
                               ICacheManager cacheManager,
                               IUserManager userManager,
                               IJurisdictionsRepository jurisdictionsRepository,
                               IMasterClientsRepository masterClientsRepository,
                               IMasterClientUsersRepository masterClientUsersRepository,
                               ILegalEntitiesRepository legalEntitiesRepository,
                               ISubmissionsRepository submissionsRepository,
                               ISubmissionsIncludingDeletedRepository submissionsIncludingDeletedRepository,
                               ILogger<SecurityManager> logger)
        {
            WorkContext = workContext;
            _cacheManager = cacheManager;
            _userManager = userManager;
            _jurisdictionsRepository = jurisdictionsRepository;
            _legalEntitiesRepository = legalEntitiesRepository;
            _submissionsRepository = submissionsRepository;
            _submissionsIncludingDeletedRepository = submissionsIncludingDeletedRepository;
            _logger = logger;
            _masterClientsRepository = masterClientsRepository;
            _masterClientUsersRepository = masterClientUsersRepository;
        }

        /// <summary>
        /// Gets the WorkContext instance.
        /// </summary>
        public IWorkContext WorkContext { get; }

        /// <summary>
        /// Gets or sets the UserId of the current user. If not set or set to null, _workContext.IdentityUserId is returned.
        /// </summary>
        public Guid UserId
        {
            get { return _userId ?? WorkContext.IdentityUserId.GetValueOrDefault(); }
            set { _userId = value; }
        }

        /// <summary>
        /// Gets the id of the jurisdiction to use for getting the correct roles/permissions.
        /// </summary>
        private Guid? ContextJurisdictionId => _contextJurisdictionId ?? WorkContext.GetProperty<Guid?>("jurisdictionId");

        /// <summary>
        /// Gets the complete list of permissions.
        /// </summary>
        /// <returns>A list with all permissions.</returns>
        public static List<string> GetAllPermissions()
        {
            var result = new List<string>();

            var permissionFields = typeof(WellKnownPermissionNames)
                                   .GetFields(BindingFlags.Public | BindingFlags.Static)
                                   .Where(f => f.FieldType == typeof(string));

            foreach (var field in permissionFields)
            {
                result.Add((string)field.GetValue(null));
            }

            return result;
        }

        /// <summary>
        /// Checks if the context user is valid and throws a ForbiddenException if not.
        /// </summary>
        /// <exception cref="ForbiddenException">The user is unknown.</exception>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task RequireUserAsync()
        {
            await SanityChecksAsync();

            if (WorkContext.User == null && !_userId.HasValue)
            {
                throw new ForbiddenException(ErrorEnum.USER_NOT_FOUND.ToErrorCode(), "A valid user is required");
            }

            RequireActiveUser();
        }

        /// <summary>
        /// Checks if the context user is valid and throws a ForbiddenException if not.
        /// </summary>
        /// <exception cref="ForbiddenException">The user is unknown.</exception>
        public void RequireUser()
        {
            RequireUserAsync().Wait();
        }

        /// <summary>
        /// Checks it the user has the given application role and throws an exception if not.
        /// </summary>
        /// <param name="role">The required role.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task RequireApplicationRoleAsync(string role)
        {
            Check.NotNullOrWhiteSpace(role, nameof(role));

            await RequireUserAsync();

            if ((await GetCachedUserRoleNamesAsync(UserId)).Contains(role, StringComparer.OrdinalIgnoreCase))
            {
                return;
            }

            throw new ForbiddenException(ErrorEnum.USER_NOT_IN_SPECIFIED_ROLE.ToErrorCode(), $"The user is not in application role '{role}'");
        }

        /// <summary>
        /// Checks if the user has one of the given application role and throws an exception if not.
        /// </summary>
        /// <param name="roles">The list of roles that the user should have one matching.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task RequireOneOfApplicationRolesAsync([NotNull] params string[] roles)
        {
            Check.NotNullOrEmpty(roles, nameof(roles));
            await RequireUserAsync();

            var userRoles = await GetCachedUserRoleNamesAsync(UserId);
            foreach (var role in roles)
            {
                if (userRoles.Contains(role, StringComparer.OrdinalIgnoreCase))
                {
                    return;
                }
            }

            var roleNames = string.Join(", ", roles);
            throw new ForbiddenException(ErrorEnum.USER_NOT_IN_SPECIFIED_ROLE.ToErrorCode(), $"The user is not in one of the application roles '{roleNames}'");
        }

        /// <summary>
        /// Checks it the user has the given application role and returns true if so.
        /// </summary>
        /// <param name="role">The required role.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<bool> UserHasApplicationRoleAsync(string role)
        {
            Check.NotNullOrWhiteSpace(role, nameof(role));

            await SanityChecksAsync();

            if ((await GetCachedUserRoleNamesAsync(UserId)).Contains(role, StringComparer.OrdinalIgnoreCase))
            {
                return true;
            }

            return false;
        }

        /// <summary>
        /// Checks if the user is a client.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<bool> UserIsClient()
        {
            return await UserHasApplicationRoleAsync(WellKnownRoleNames.Client);
        }

        /// <inheritdoc />
        public async Task RequireManagementUserAsync()
        {
            _logger.LogTrace("Checking if user {UserId} is a management user", UserId);
            await RequireUserAsync();

            if (await UserIsClient())
            {
                throw new ForbiddenException(ApplicationErrors.USER_IS_NOT_MANAGEMENT_USER.ToErrorCode(), "The user is not a management user");
            }
        }

        /// <inheritdoc />
        public async Task RequireClientUserAsync()
        {
            _logger.LogTrace("Checking if user {UserId} is a client user", UserId);
            await RequireUserAsync();

            if (!await UserIsClient())
            {
                throw new ForbiddenException(ApplicationErrors.USER_IS_NOT_CLIENT_USER.ToErrorCode(), "The user is not a client user");
            }
        }

        /// <summary>
        /// Checks it the user has one of the given application role and returns true if so.
        /// </summary>
        /// <param name="roles">The list of roles that the user should have one matching.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<bool> UserHasOneOfApplicationRolesAsync([NotNull] params string[] roles)
        {
            Check.NotNullOrEmpty(roles, nameof(roles));

            var userRoles = await GetCachedUserRoleNamesAsync(UserId);
            foreach (var role in roles)
            {
                if (userRoles.Contains(role, StringComparer.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Checks it the user has one of the given application role and returns true if so.
        /// </summary>
        /// <param name="roles">The list of roles that the user should have one matching.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<bool> UserHasOneOfApplicationRolesAsync([NotNull] ICollection<string> roles)
        {
            Check.NotNullOrEmpty(roles, nameof(roles));

            await SanityChecksAsync();

            var userRoles = await GetCachedUserRoleNamesAsync(UserId);
            foreach (var role in roles)
            {
                if (userRoles.Contains(role, StringComparer.OrdinalIgnoreCase))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Checks if the user has the given permission.
        /// </summary>
        /// <param name="permission">The permission to check.</param>
        /// <returns>True if user has the permission.</returns>
        public async Task<bool> HasManagementPermissionAsync(string permission)
        {
            await RequireManagementUserAsync();

            return await new PermissionHandler(this).HasPermissionAsync(permission);
        }

        /// <summary>
        /// Checks if the user has one of the given permission.
        /// </summary>
        /// <param name="permissions">The permission to check.</param>
        /// <returns>True if user has the permission.</returns>
        public async Task<bool> HasOneOfPermissionsAsync([NotNull] string[] permissions)
        {
            Check.NotNull(permissions, nameof(permissions));
            await RequireManagementUserAsync();

            var handler = new PermissionHandler(this);
            foreach (var permission in permissions)
            {
                if (await handler.HasPermissionAsync(permission))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Checks if the user has the given permission and throws an exception if not.
        /// </summary>
        /// <param name="permission">The permission to check.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task RequireManagementPermissionAsync(string permission)
        {
            Check.NotNullOrWhiteSpace(permission, nameof(permission));
            await RequireManagementUserAsync();

            var hasPermissionAsync = await new PermissionHandler(this).HasPermissionAsync(permission);
            if (hasPermissionAsync == false)
            {
                throw new ForbiddenException(
                        ApplicationErrors.NO_PERMISSION.ToErrorCode(),
                        $"The user does not have the permission '{permission}' to perform the current action");
            }
        }

        /// <summary>
        /// Checks if the user has the given permission and throws an exception if not.
        /// </summary>
        /// <param name="permissions">The permissions to check.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task RequireOneOfPermissionsAsync(params string[] permissions)
        {
            Check.NotNullOrEmpty(permissions, nameof(permissions));
            await RequireManagementUserAsync();

            if ((await HasOneOfPermissionsAsync(permissions)) == false)
            {
                var permissionNames = string.Join(", ", permissions);
                throw new ForbiddenException(
                        ApplicationErrors.NO_PERMISSION.ToErrorCode(),
                        $"The user does not have one of the permission '{permissionNames}' to perform the current action");
            }
        }

        /// <summary>
        /// Checks it the user has access to the given company.
        /// </summary>
        /// <param name="legalEntityId">The id of the company to check access for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<bool> ClientUserHasAccessToCompanyAsync(Guid legalEntityId)
        {
            await RequireClientUserAsync();

            var company = await _legalEntitiesRepository.CheckLegalEntityByIdAsync(legalEntityId);

            // Access through master client?
            var hasAccess = await ClientUserHasAccessToMasterClientAsync(company.MasterClientId);

            return hasAccess;
        }

        /// <summary>
        /// Checks it the user has access to the given master client.
        /// </summary>
        /// <param name="masterClientId">The id of the master client to check access for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<bool> ClientUserHasAccessToMasterClientAsync(Guid masterClientId)
        {
            await RequireClientUserAsync();

            await _masterClientsRepository.CheckMasterClientByIdAsync(masterClientId);

            var any = await _masterClientUsersRepository.AnyByConditionAsync(mcu => mcu.MasterClientId == masterClientId &&
                                                                                    mcu.UserId == UserId);
            return any;
        }

        /// <summary>
        /// Checks it the user has access to the given jurisdiction.
        /// </summary>
        /// <param name="jurisdictionId">The id of the jurisdiction to check access for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task<bool> ManagementUserHasAccessToJurisdictionAsync(Guid jurisdictionId)
        {
            await RequireManagementUserAsync();

            await _jurisdictionsRepository.CheckJurisdictionByIdAsync(jurisdictionId);

            if (await UserHasAnyPermissionToJurisdictionAsync(jurisdictionId))
            {
                return true;
            }

            return false;
        }

        /// <inheritdoc/>
        public async Task RequireManagementAccessToJurisdictionAsync(Guid jurisdictionId)
        {
            if (!(await ManagementUserHasAccessToJurisdictionAsync(jurisdictionId)))
            {
                throw new ForbiddenException(
                    ApplicationErrors.NO_PERMISSION.ToErrorCode(),
                    "The user does not have the permission 'access to jurisdiction' to perform the current action");
            }
        }

        /// <inheritdoc/>
        public async Task RequireManagementAccessToCompanyAsync(Guid legalEntityId)
        {
            var company = await _legalEntitiesRepository.CheckLegalEntityByIdAsync(legalEntityId);

            if (!(await ManagementUserHasAccessToJurisdictionAsync(company.JurisdictionId.GetValueOrDefault())))
            {
                throw new ForbiddenException(
                    ApplicationErrors.NO_PERMISSION.ToErrorCode(),
                    "The user does not have the permission 'access to company' to perform the current action");
            }
        }

        /// <inheritdoc/>
        public async Task RequireClientAccessToCompanyAsync(Guid legalEntityId)
        {
            if (!(await ClientUserHasAccessToCompanyAsync(legalEntityId)))
            {
                throw new ForbiddenException(
                        ApplicationErrors.NO_PERMISSION.ToErrorCode(),
                        "The user does not have the permission 'access to company' to perform the current action");
            }
        }

        /// <inheritdoc/>
        public async Task RequireClientAccessToMasterClientAsync(Guid masterClientId)
        {
            if (!(await ClientUserHasAccessToMasterClientAsync(masterClientId)))
            {
                throw new ForbiddenException(
                        ApplicationErrors.NO_PERMISSION.ToErrorCode(),
                        "The user does not have the permission 'access to masterclient' to perform the current action");
            }
        }

        /// <inheritdoc />
        public async Task RequireClientAccessToSubmissionAsync(Guid submissionId, bool allowDeleted = false)
        {
            var submission = await _submissionsRepository.CheckSubmissionByIdAsync(submissionId);

            await RequireClientAccessToCompanyAsync(submission.LegalEntityId);
        }

        /// <inheritdoc />
        public async Task RequireManagementPermissionForAuditEntityAsync(string entityName)
        {
            Check.NotNullOrWhiteSpace(entityName, nameof(entityName));

            var requiredPermission = entityName.ToUpperInvariant() switch
            {
                "APPLICATIONUSER" => WellKnownPermissionNames.Users_ViewLog,
                "LEGALENTITY" => WellKnownPermissionNames.Companies_View_Log,
                "MASTERCLIENT" => WellKnownPermissionNames.MasterClients_View_Log,
                "SUBMISSION" => WellKnownPermissionNames.Companies_View_Log, // todo specific permission?
                _ => throw new ArgumentOutOfRangeException(nameof(entityName), entityName, "Unknown entity name")
            };

            await RequireManagementPermissionAsync(requiredPermission);
        }

        /// <inheritdoc />
        public async Task RequireManagementPermissionForLegalEntityAsync(string permission, Guid legalEntityId)
        {
            Check.NotNullOrWhiteSpace(permission, nameof(permission));
            Check.NotDefaultOrNull<Guid>(legalEntityId, nameof(legalEntityId));

            await RequireManagementUserAsync();

            var legalEntity = await _legalEntitiesRepository.CheckLegalEntityByIdAsync(legalEntityId);

            if (!(await GetJurisdictionsForManagementPermissionAsync(permission)).Contains(legalEntity.JurisdictionId.GetValueOrDefault()))
            {
                throw new ForbiddenException(
                    ApplicationErrors.NO_PERMISSION.ToErrorCode(),
                    $"The user does not have the permission '{permission}' to perform the current action for legal entity {legalEntityId}");
            }
        }

        /// <inheritdoc />
        public async Task RequireManagementPermissionForJurisdictionAsync(string permission, Guid jurisdictionId)
        {
            Check.NotNullOrWhiteSpace(permission, nameof(permission));
            Check.NotDefaultOrNull<Guid>(jurisdictionId, nameof(jurisdictionId));

            await RequireManagementUserAsync();

            if (!(await GetJurisdictionsForManagementPermissionAsync(permission)).Contains(jurisdictionId))
            {
                throw new ForbiddenException(
                    ApplicationErrors.NO_PERMISSION.ToErrorCode(),
                    $"The user does not have the permission '{permission}' to perform the current action for jurisdiction {jurisdictionId}");
            }
        }

        /// <inheritdoc />
        public async Task RequireManagementPermissionForSubmissionAsync(string permission, Guid submissionId, bool allowDeleted = false)
        {
            Check.NotNullOrWhiteSpace(permission, nameof(permission));
            Check.NotDefaultOrNull<Guid>(submissionId, nameof(submissionId));

            await RequireManagementUserAsync();

            var submission = await _submissionsIncludingDeletedRepository.CheckSubmissionByIdAsync(submissionId);

            await RequireManagementPermissionForLegalEntityAsync(permission, submission.LegalEntityId);
        }

        /// <summary>
        /// Gets a dictionary with per permission the list of roles that have that permission.
        /// </summary>
        /// <returns>A dictionary with per permission the list of roles that have that permission.</returns>
        public Dictionary<string, IList<string>> GetPermissionsWithRoles()
        {
            if (_permissionsWithRoles.Count == 0)
            {
                new CommonPermissionsBuilder().BuildPermissionsWithRoles(_permissionsWithRoles);
                new NevisPermissionsBuilder().BuildPermissionsWithRoles(_permissionsWithRoles);
                new PanamaPermissionsBuilder().BuildPermissionsWithRoles(_permissionsWithRoles);
                new BahamasPermissionsBuilder().BuildPermissionsWithRoles(_permissionsWithRoles);
                new BVIPermissionsBuilder().BuildPermissionsWithRoles(_permissionsWithRoles);

                // Add other jurisdiction permission builders here
            }

            return _permissionsWithRoles;
        }

        /// <summary>
        /// Gets the ids of the jurisdictions that the user has the given permission for.
        /// </summary>
        /// <remarks>
        /// You can use this for example to check in which jurisdictions a company search can be performed by the user.
        /// </remarks>
        /// <param name="permission">The permission to get the jurisdictions for.</param>
        /// <returns>A list of jurisdiction ids.</returns>
        public async Task<List<Guid>> GetJurisdictionsForManagementPermissionAsync(string permission)
        {
            ArgumentNullException.ThrowIfNull(permission, nameof(permission));

            await RequireManagementUserAsync();

            var result = new List<Guid>();

            var allJurisdictions = await _jurisdictionsRepository.FindAllAsync();

            try
            {
                foreach (var jurisdiction in allJurisdictions)
                {
                    _contextJurisdictionId = jurisdiction.Id;

                    if (await HasManagementPermissionAsync(permission))
                    {
                        result.Add(jurisdiction.Id);
                    }
                }
            }
            finally
            {
                _contextJurisdictionId = null;
            }

            return result;
        }

        /// <summary>
        /// Gets the ids of the jurisdictions that the user has any permission for.
        /// </summary>
        /// <remarks>
        /// You can use this for example to check in which jurisdictions a company search can be performed by the user.
        /// </remarks>
        /// <returns>A list of jurisdiction ids.</returns>
        public async Task<List<Guid>> GetAllJurisdictionsForManagementPermissionsAsync()
        {
            await RequireManagementUserAsync();

            var result = new List<Guid>();

            var allJurisdictions = await _jurisdictionsRepository.FindAllAsync();

            foreach (var jurisdiction in allJurisdictions)
            {
                if (await UserHasAnyPermissionToJurisdictionAsync(jurisdiction.Id))
                {
                    result.Add(jurisdiction.Id);
                }
            }

            return result;
        }

        /// <summary>
        /// Validates the jurisdictions access for the current user.
        /// </summary>
        /// <param name="jurisdictionIds">The unique identifiers of the jurisdictions to validate access.</param>
        /// <returns>The result of the task.</returns>
        public async Task ValidateJurisdictionsAccess(IEnumerable<Guid> jurisdictionIds)
        {
            var allJurisdictions = await GetAllJurisdictionsForManagementPermissionsAsync();
            var missingJurisdictions = jurisdictionIds
                .Where(jurisdictionId => !allJurisdictions.Contains(jurisdictionId))
                .ToList();

            if (missingJurisdictions.Any())
            {
                throw new ForbiddenException(
                    ApplicationErrors.INVALID_JURISDICTION_ACCESS.ToErrorCode(),
                    $"The user not have access to the following jurisdictions: {string.Join(", ", missingJurisdictions)}");
            }
        }
        #region Private methods

        /// <summary>
        /// Get the complete list of application roles, optionally having the propertyname started with the prefix.
        /// </summary>
        /// <returns>A list of role names.</returns>
#pragma warning disable CA1859 Change return type of method 'ListAllApplicationRoles' from 'System.Collections.Generic.IReadOnlyCollection<string>' to 'System.Collections.Generic.List<string>' for improved performance
        private static IReadOnlyCollection<string> ListAllApplicationRoles(string propertyPrefix = null)
#pragma warning restore CA1859
        {
            var result = new List<string>();

            // Get all roles for the whole portal.
            var roleFields = typeof(WellKnownRoleNames)
                             .GetFields(BindingFlags.Public | BindingFlags.Static)
                             .Where(f => f.FieldType == typeof(string));

            foreach (var field in roleFields)
            {
                if (propertyPrefix == null || field.Name.StartsWith($"{propertyPrefix}_", StringComparison.OrdinalIgnoreCase))
                {
                    var roleName = (string)field.GetValue(null);
                    result.Add(roleName);
                }
            }

            return result;
        }

        private void RequireActiveUser()
        {
            if (WorkContext.User != null && !WorkContext.User.IsActive)
            {
                throw new ForbiddenException(ApplicationErrors.USER_NOT_ACTIVE.ToErrorCode(), "A valid active user is required");
            }
        }

        /// <summary>
        /// Checks if the current user (context) has at least one of the jurisdiction permissions.
        /// </summary>
        /// <param name="jurisdictionId">The id of the jurisdiction to check.</param>
        /// <returns>True if the user has at least one permission for the jurisdiction.</returns>
        private async Task<bool> UserHasAnyPermissionToJurisdictionAsync(Guid jurisdictionId)
        {
            var savedJurisdictionId = WorkContext.GetProperty("jurisdictionId");

            try
            {
                WorkContext.SetProperty("jurisdictionId", jurisdictionId);

                var allPermissions = GetAllPermissions();
                foreach (var permission in allPermissions)
                {
                    if (await HasManagementPermissionAsync(permission))
                    {
                        return true;
                    }
                }

                return false;
            }
            finally
            {
                WorkContext.SetProperty("jurisdictionId", savedJurisdictionId);
            }
        }

        /// <summary>
        /// Performs some sanity checks on the user.
        /// </summary>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        private async Task SanityChecksAsync()
        {
            if (!WorkContext.IdentityUserId.HasValue && !_userId.HasValue)
            {
                throw new UnauthorizedAccessException($"{ApplicationErrors.USER_ID_NOT_IN_HEADER.ToErrorCode()}: No user passed in header (x-userid)");
            }

            if (WorkContext.User == null && WorkContext.IdentityUserId.HasValue)
            {
                WorkContext.User = await _userManager.GetUserByIdAsync(UserId);

                if (WorkContext.User == null)
                {
                    throw new ForbiddenException(ApplicationErrors.USER_ID_NOT_FOUND.ToErrorCode(), "User passed in header (x-userid) not found");
                }
            }
        }

        /// <summary>
        /// Gets the role names for the given user and uses cache for this request.
        /// </summary>
        /// <param name="userId">ID of the user to get the roles for.</param>
        /// <returns>A IReadOnly list with the names.</returns>
        private async Task<IReadOnlyList<string>> GetCachedUserRoleNamesAsync(Guid userId)
        {
            string key = $"user.applicationrolenames.{userId}.{ContextJurisdictionId}";

            return await _cacheManager.GetAsync<IReadOnlyList<string>>(key, async () =>
            {
                var roleNames = (await _userManager.GetUserRoleNamesAsync(UserId)).Select(x => x.ToLower()).ToList();

                // TODO remove after troubleshooting issue 17328
                _logger.LogTrace("User {UserId} has the following roles: ({Roles}).", UserId, string.Join(", ", roleNames));

                var allowedRoleNames = await GetAllowedRoleNames();

                var result = new List<string>();

                foreach (var role in allowedRoleNames)
                {
                    if (roleNames.Contains(role, StringComparer.OrdinalIgnoreCase))
                    {
                        result.Add(role);
                    }
                }

                return result;
            });
        }

        /// <summary>
        /// Gets all jurisdictions and collects the roles.
        /// </summary>
        private async Task PrepareJurisdictionRolesAsync()
        {
            var allJurisdictions = await _jurisdictionsRepository.FindAllAsync();

            foreach (var jurisdiction in allJurisdictions)
            {
                PrepareJurisdictionRoles(jurisdiction.Code, jurisdiction.Id);
            }
        }

        /// <summary>
        /// Adds all roles that are prefixed with the code of the jurisdiction to the list for that jurisdiction.
        /// </summary>
        /// <param name="prefix">The prefix that the role should start with, i.e. 'Nevis.'.</param>
        /// <param name="jurisdictionId">The id of the jurisdiction.</param>
        private void PrepareJurisdictionRoles(string prefix, Guid jurisdictionId)
        {
            var jurisdictionRoles = ListAllApplicationRoles(prefix).Where(x => x.StartsWith($"{prefix}.", StringComparison.OrdinalIgnoreCase));

            _jurisdictionRoleNames[jurisdictionId] = jurisdictionRoles.ToList();
        }

        /// <summary>
        /// Gets all allowed roles for the user in the workcontext taking the roles for the context jurisdiction into account.
        /// </summary>
        private async Task<IReadOnlyCollection<string>> GetAllowedRoleNames()
        {
            var result = new List<string>();

            var jurisdictionId = ContextJurisdictionId;
            if (jurisdictionId.HasValue)
            {
                // Need to load the names of the roles that are allowed for the current jurisdiction (context)?
                if (_jurisdictionRoleNames.Count == 0)
                {
                    await PrepareJurisdictionRolesAsync();
                }

                // Independent of jurisdiction
                var commonRoles = ListAllApplicationRoles("Common");
                result.AddRange(commonRoles);

                // Dependent on the jurisdiction
                if (_jurisdictionRoleNames.TryGetValue(jurisdictionId.Value, out IList<string> value))
                {
                    foreach (var roleName in value)
                    {
                        result.Add(roleName);
                    }
                }
            }
            else
            {
                // Get all roles for the whole portal.
                result = ListAllApplicationRoles().ToList();
            }

            return result;
        }

        #endregion
    }
}
