﻿using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.Announcements;
using NetProGroup.Trust.DataManager.Announcements;
using NetProGroup.Trust.DataManager.InboxItems;
using NetProGroup.Trust.Domain.Inboxes;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.DataManagers
{
    public class Test_Announcements : TestBase
    {

        private IInboxDataManager _inboxDataManager;
        private IInboxReadStatusesRepository _inboxReadStatusRepository;
        private IAnnouncementDataManager _announcementDataManager;

        [SetUp]
        public async Task Setup()
        {
            _announcementDataManager = _server.Services.GetRequiredService<IAnnouncementDataManager>();
            _inboxDataManager = _server.Services.GetRequiredService<IInboxDataManager>();
            _inboxReadStatusRepository = _server.Services.GetRequiredService<IInboxReadStatusesRepository>();
        }

        [Test]
        public async Task CreateUpdateAnnouncementAsync_InboxMessage_MarkAsRead()
        {
            // Arrange
            var createDto = new CreateUpdateAnnouncementDTO
            {
                Subject = "Test Announcement",
                EmailSubject = "Test Email Subject",
                Body = "This is a test announcement body.",
                IncludeAttachments = false,
                SendNow = true,
                SendAt = DateTime.UtcNow.AddMinutes(1),
                MasterClientCodes = new List<string> { _masterClient.Code },
                SendToAllMasterClients = false,
                SendToAllJurisdictions = false,
                JurisdictionId = JurisdictionBahamasId,
                UserIds = new List<Guid> { ClientUser.Id }
            };

            //Create announcement
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);

            // Retrieve the first unread inbox message for the master client
            var unreadInboxMessage = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: false)).Single();

            // Act
            await _inboxDataManager.CreateInboxReadStatusIfNotExistsAsync(unreadInboxMessage.Id, ClientUser.Id, DateTime.UtcNow);

            //Get the inbox messages status 
            var readMessage = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: true)).Single();

            // Assert
            readMessage.IsRead.Should().BeTrue("The inbox message should be marked as read after creating the read status.");
        }

        [Test]
        public async Task CreateInboxReadStatusIfNotExistsAsync_DoesNotDuplicateReadStatus()
        {
            // Arrange
            var createDto = new CreateUpdateAnnouncementDTO
            {
                Subject = "Test Announcement Duplicate ReadStatus",
                EmailSubject = "Test Email Subject",
                Body = "This is a test announcement body.",
                IncludeAttachments = false,
                SendNow = true,
                SendAt = DateTime.UtcNow.AddMinutes(1),
                MasterClientCodes = new List<string> { _masterClient.Code },
                SendToAllMasterClients = false,
                SendToAllJurisdictions = false,
                JurisdictionId = JurisdictionBahamasId,
                UserIds = new List<Guid> { ClientUser.Id }
            };

            // Create announcement
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);

            // Retrieve the first unread inbox message for the master client
            var unreadInboxMessage = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: false)).Single();
            
            // Act - first call
            await _inboxDataManager.CreateInboxReadStatusIfNotExistsAsync(unreadInboxMessage.Id, ClientUser.Id, DateTime.UtcNow);

            // Get all read messages for the user
            var readMessage = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: true)).Single();

            var readStatuses = await _inboxReadStatusRepository.FindByConditionAsync(status => status.UserId == ClientUser.Id);
            var readStatusBefore1 = readStatuses.Should().ContainSingle().Which;

            // There should be only one read status for this message and user
            // (Assuming the system only allows one read status per message/user, so IsRead is true and ReadAt is not overwritten)
            var readAt1 = readMessage.ReadAt;

            // Call again and check that ReadAt is not changed.
            await _inboxDataManager.CreateInboxReadStatusIfNotExistsAsync(unreadInboxMessage.Id, ClientUser.Id, DateTime.UtcNow.AddMinutes(2));

            var readMessageAfter = (await _inboxDataManager.GetInboxMessagesAsync(ClientUser.Id, isRead: true)).Single();

            var readStatusesAfter = await _inboxReadStatusRepository.FindByConditionAsync(status => status.UserId == ClientUser.Id);

            // Assert
            readMessage.IsRead.Should().BeTrue("The message should be marked as read.");
            readMessageAfter.ReadAt.Should().Be(readAt1, "ReadAt should not be updated on duplicate call.");
            readStatusesAfter.Should().ContainSingle("No additional read status should be created if one already exists.")
                .Which.Id.Should().Be(readStatusBefore1.Id, "The read status should not be replaced.");
        }

        [Test]
        public async Task UpdateAnnouncement_ShouldNotChangeStatusFromScheduledToDraft()
        {
            // Arrange
            var createDto = new CreateUpdateAnnouncementDTO
            {
                Subject = "Scheduled Announcement",
                EmailSubject = "Scheduled Email Subject",
                Body = "This is a scheduled announcement body.",
                IncludeAttachments = false,
                SendNow = false,
                SendAt = DateTime.UtcNow.AddMinutes(10),
                MasterClientCodes = new List<string> { _masterClient.Code },
                SendToAllMasterClients = false,
                SendToAllJurisdictions = false,
                JurisdictionId = JurisdictionBahamasId,
                UserIds = new List<Guid> { ClientUser.Id }
            };

            var announcementId = await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);
            var announcement = (await _announcementDataManager.FilterAnnouncementsAsync(new FilterAnnouncementsDTO())).Single(x => x.Id == announcementId);
            var originalStatus = announcement.Status;
            originalStatus.Should().Be(AnnouncementStatus.Scheduled);

            // Update the announcement with changes (should not revert to Draft)
            createDto.Id = announcementId;
            createDto.Subject = "Updated Scheduled Announcement";
            createDto.Body = "This is an updated scheduled announcement body.";

            // Act
            await _announcementDataManager.CreateUpdateAnnouncementAsync(createDto);

            // Assert
            var updatedAnnouncement = (await _announcementDataManager.FilterAnnouncementsAsync(new FilterAnnouncementsDTO())).Single(x => x.Id == announcementId);
            updatedAnnouncement.Status.Should().Be(originalStatus, "Status should not change from Scheduled to Draft when updating announcement.");
        }

    }
}
