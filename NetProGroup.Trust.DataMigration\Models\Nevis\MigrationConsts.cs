namespace NetProGroup.Trust.DataMigration.Models.Nevis;

/// <summary>
/// Contains constant values and collections used in the migration process.
/// </summary>
public static class MigrationConsts
{
    /// <summary>
    /// A set of valid business activities used in the migration process.
    /// </summary>
    public static readonly IReadOnlySet<string> ValidBusinessActivities = new HashSet<string>
    {
        "Banking Business",
        "Insurance Business",
        "Fund Management Business",
        "Finance and Leasing Business",
        "Headquarters Business",
        "Shipping Business",
        "Holding Business",
        "Intellectual Property Business",
        "Distribution and Service Centre Business"
    };

    /// <summary>
    /// Constants for Nevis Invoice Configurations.
    /// </summary>
    public static class NevisInvoiceConfigurations
    {
        /// <summary>
        /// The collection name for Nevis Invoice Configurations.
        /// </summary>
        public const string CollectionName = "nevisinvoiceconfigurations";

        /// <summary>
        /// The display name for Nevis Invoice Configurations.
        /// </summary>
        public const string DisplayName = "Nevis Invoice Configurations";
    }

    /// <summary>
    /// Constants for Entries.
    /// </summary>
    public static class Entries
    {
        /// <summary>
        /// The collection name for Entries.
        /// </summary>
        public const string CollectionName = "entries";

        /// <summary>
        /// The display name for Entries.
        /// </summary>
        public const string DisplayName = "Submissions";
    }

    /// <summary>
    /// Constants for Companies.
    /// </summary>
    public static class Companies
    {
        /// <summary>
        /// The collection name for Companies.
        /// </summary>
        public const string CollectionName = "companies";

        /// <summary>
        /// The display name for Companies.
        /// </summary>
        public const string DisplayName = "Companies";
    }
}
