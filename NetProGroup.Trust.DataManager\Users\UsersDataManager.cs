﻿// <copyright file="UsersDataManager.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using System.Xml;
using AutoMapper;
using AutoMapper.QueryableExtensions;
using NetProGroup.Framework.Exceptions;
using NetProGroup.Framework.Services.Identity.EFModels;
using NetProGroup.Framework.Services.Identity.Models;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Framework.Services.Identity.Services;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.Errors;
using NetProGroup.Trust.Application.Contracts.Users;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.Users.RequestResponses;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Users;
using NetProGroup.Framework.Helpers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using X.PagedList;
using NetProGroup.Trust.Domain.Shared.Defines;
using NetProGroup.Trust.Application.Contracts.Shared;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Roles;
using System.Linq.Expressions;
using NetProGroup.Trust.Application.Contracts.MasterClients;
using NetProGroup.Trust.Application.Contracts.Users.TermsConditions;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.Domain.Repository.Extensions;

namespace NetProGroup.Trust.DataManager.Users
{
    /// <summary>
    /// Manager for user data.
    /// </summary>
    public class UsersDataManager : IUsersDataManager
    {
        private readonly IMapper _mapper;
        private readonly IConfiguration _configuration;

        private readonly IUserAttributesRepository _userAttributesRepository;
        private readonly IUserRepository _usersRepository;
        private readonly IApplicationUsersRepository _applicationUsersRepository;
        private readonly IUserManager _userManager;

        private readonly IMasterClientsRepository _masterClientsRepository;
        private readonly IMasterClientUsersRepository _masterClientUsersRepository;
        private readonly Framework.Services.Configuration.IConfigurationManager _configurationManager;

        private readonly Dictionary<Guid, List<UserAttribute>> _cache = new Dictionary<Guid, List<UserAttribute>>();
        private readonly ISystemAuditManager _systemAuditManager;

        /// <summary>
        /// Initializes a new instance of the <see cref="UsersDataManager"/> class.
        /// </summary>
        /// <param name="mapper">Mapper instance.</param>
        /// <param name="configuration">Instance of the configuration.</param>
        /// <param name="userAttributesRepository">Instance of the UserAttributes repository.</param>
        /// <param name="usersRepository">Instance of the ApplicationUsers repository.</param>
        /// <param name="applicationUsersRepository">Instance of the ApplicationUser repository.</param>
        /// <param name="userManager">Instance of the framework UserManager.</param>
        /// <param name="masterClientsRepository">Instance of the MasterClients repository.</param>
        /// <param name="masterClientUsersRepository">Instance of the MasterClientUsers repository.</param>
        /// <param name="configurationManager">Instance of the configuration manager.</param>
        /// <param name="systemAuditManager">Instance of the system audit manager.</param>
        public UsersDataManager(IMapper mapper,
            IConfiguration configuration,
            IUserAttributesRepository userAttributesRepository,
            IUserRepository usersRepository,
            IApplicationUsersRepository applicationUsersRepository,
            IUserManager userManager,
            IMasterClientsRepository masterClientsRepository,
            IMasterClientUsersRepository masterClientUsersRepository,
            Framework.Services.Configuration.IConfigurationManager configurationManager,
            ISystemAuditManager systemAuditManager)
        {
            _mapper = mapper;
            _configuration = configuration;

            _userAttributesRepository = userAttributesRepository;
            _usersRepository = usersRepository;
            _applicationUsersRepository = applicationUsersRepository;
            _userManager = userManager;
            _masterClientsRepository = masterClientsRepository;
            _masterClientUsersRepository = masterClientUsersRepository;

            _configurationManager = configurationManager;
            _systemAuditManager = systemAuditManager;
        }

        /// <summary>
        /// Gets the configuration object.
        /// </summary>
        public IConfiguration Configuration => _configuration;

        /// <summary>
        /// Creates a new user.
        /// </summary>
        /// <param name="createUserModel">The user to create.</param>
        /// <returns><see cref="Task{ApplicationUserDTO}"/> representing the asynchronous operation holdign the resultin ApplicationUserDTO.</returns>
        public async Task<ApplicationUserDTO> CreateUserAsync(CreateUserDTO createUserModel)
        {
            ArgumentNullException.ThrowIfNull(createUserModel, nameof(createUserModel));

            var user = await _usersRepository.FindByUserByPredicateAsync(x => x.Email == createUserModel.Email);
            if (user == null)
            {
                user = new ApplicationUser
                {
                    Id = Guid.NewGuid(),
                    Email = createUserModel.Email,
                    NormalizedEmail = createUserModel.Email.ToUpper(),
                    UserName = createUserModel.Email,
                    NormalizedUserName = createUserModel.Email.ToUpper(),
                    DisplayName = createUserModel.DisplayName,
                    Surname = createUserModel.LastName,
                    Name = createUserModel.FirstName,
                    IsActive = true
                };

                await _usersRepository.CreateUserAsync(user);
            }
            else
            {
                throw new ConstraintException(ApplicationErrors.USER_ALREADY_ADDED.ToErrorCode(), $"User with emailaddress '{createUserModel.Email}' already exists.");
            }

            return await _userManager.GetUserByIdAsync(user.Id);
        }

        /// <inheritdoc />
        public async Task<IPagedList<ListApplicationUsersDTO>> ListUsersAsync(UsersRequestDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var usersWithRoles = GetUsersWithRolesQuery(GetApplicationUserPredicate(request.Filter));

            var result = await usersWithRoles.FindByConditionAsPagedListMappedAsync<UserDto, ListApplicationUsersDTO>(
                dto => true,
                _mapper.ConfigurationProvider,
                pageNumber: request.PageNumber,
                pageSize: request.PageSize,
                options: q => q.TagWithCallSite(),
                optionsMapped: q => ApplySorting(q, request));

            return result;
        }

        /// <inheritdoc />
        public async Task<PCPApplicationUserDTO> GetUserByIdAsync(Guid id)
        {
            ArgumentNullException.ThrowIfNull(id, nameof(id));

            var usersWithRoles = GetUsersWithRolesQuery(x => x.Id == id);

            var result = await usersWithRoles.ProjectTo<PCPApplicationUserDTO>(_mapper.ConfigurationProvider)
                                       .AsNoTracking()
                                       .TagWithCallSite()
                                       .SingleOrDefaultAsync();

            return result;
        }

        /// <inheritdoc />
        public async Task<bool> BlockUnblockUserAsync(Guid id, BlockUserDTO request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            ArgumentNullException.ThrowIfNull(id, nameof(id));
            var user = await _usersRepository.CheckUserByIdAsync(id);
            user.LockoutEnabled = request.IsBlocked;

            if (request.IsBlocked)
            {
                await _systemAuditManager.AddActivityLogAsync(
                   user,
                   ActivityLogActivityTypes.UserBlocked,
                   "User is blocked",
                   "User is blocked");
            }
            else
            {
                await _systemAuditManager.AddActivityLogAsync(
                   user,
                   ActivityLogActivityTypes.UserUnblocked,
                   "User is unblocked",
                   "User is unblocked");
            }

            await _usersRepository.UpdateUserAsync(user);
            return request.IsBlocked;
        }

        /// <inheritdoc/>
        public async Task<TermsConditionsStatusDTO> GetTermsConditionsStatusAsync(Guid userId)
        {
            await _usersRepository.CheckUserByIdAsync(userId);

            var attr = (await GetUserAttributesAsync(userId)).FirstOrDefault(x => x.Key.Equals(UserAttributeKeys.TermsConditionsVersion, StringComparison.OrdinalIgnoreCase));
            var acceptedVersion = attr?.Value;

            return new TermsConditionsStatusDTO
            {
                IsAccepted = !string.IsNullOrEmpty(acceptedVersion),
                Version = acceptedVersion,
                AcceptedAt = attr?.UpdatedAt
            };
        }

        /// <inheritdoc/>
        public async Task AcceptTermsConditionsAsync(AcceptTermsConditionsRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var userId = request.UserId;
            var version = request.Version;

            var user = await _usersRepository.CheckUserByIdAsync(userId);

            await SetAttributeValueAsync(userId, UserAttributeKeys.TermsConditionsVersion, version, true);

            await _systemAuditManager.AddActivityLogAsync(
                user,
                ActivityLogActivityTypes.UserTermsConditionsAccepted,
                "Terms and Conditions accepted.",
                $"User accepted Terms and Conditions version {version}.",
                saveChanges: true);
        }

        /// <inheritdoc/>
        public async Task SetUserMasterClientsAsync(UserMasterClientsDTO userMasterClientsDTO)
        {
            ArgumentNullException.ThrowIfNull(userMasterClientsDTO, nameof(userMasterClientsDTO));

            await _usersRepository.CheckUserByIdAsync(userMasterClientsDTO.UserId);

            var masterClientUsers = await _masterClientUsersRepository.FindByConditionAsync(x => x.UserId == userMasterClientsDTO.UserId);

            foreach (var masterClientId in userMasterClientsDTO.MasterClientIds)
            {
                if (!masterClientUsers.Any(x => x.MasterClientId == masterClientId))
                {
                    await _masterClientsRepository.CheckMasterClientByIdAsync(masterClientId, throwNotFound: false);

                    await _masterClientUsersRepository.InsertAsync(new MasterClientUser(new Guid()) { UserId = userMasterClientsDTO.UserId, MasterClientId = masterClientId }, saveChanges: false);
                }
            }

            if (userMasterClientsDTO.RemoveUnmentionedMasterClients)
            {
                var toRemove = masterClientUsers.Where(x => !userMasterClientsDTO.MasterClientIds.Contains(x.MasterClientId));
                await _masterClientUsersRepository.DeleteAsync(toRemove, saveChanges: false);
            }

            await _masterClientUsersRepository.SaveChangesAsync();
        }

        /// <summary>
        /// Gets the method to use for MFA.
        /// </summary>
        /// <param name="userId">Id of the user to get the setting for.</param>
        /// <returns>The found method.</returns>
        public async Task<string> GetMFAMethodAsync(Guid userId)
        {
            await _usersRepository.CheckUserByIdAsync(userId);

            var method = await GetAttributeValueAsync(userId, UserAttributeKeys.MFAMethod, string.Empty);

            if (method == "authenticator")
            {
                // Check if enabled. If not, the user did not complete verification the last time.
                var mfaHelper = new MFAHelper(this);
                if (!await mfaHelper.IsMFAEnabledAsync(userId))
                {
                    return string.Empty;
                }
            }

            return method;
        }

        /// <summary>
        /// Sets the method to use for MFA.
        /// </summary>
        /// <param name="userId">Id of the user to get the setting for.</param>
        /// <param name="value">The method to use.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task SetMFAMethodAsync(Guid userId, string value)
        {
            ArgumentNullException.ThrowIfNullOrWhiteSpace(value, nameof(value));

            await _usersRepository.CheckUserByIdAsync(userId);

            switch (value.ToLower())
            {
                case "authenticator":
                case "email-code":
                    {
                        var method = await GetAttributeValueAsync(userId, UserAttributeKeys.MFAMethod, string.Empty);

                        if (!value.Equals(method, StringComparison.OrdinalIgnoreCase))
                        {
                            var mfaHelper = new MFAHelper(this);
                            await mfaHelper.ResetTFAAsync(userId);

                            await SetAttributeValueAsync(userId, UserAttributeKeys.MFAMethod, value);

                            await _masterClientUsersRepository.SaveChangesAsync();
                        }

                        break;
                    }

                default:
                    {
                        throw new NetProGroup.Framework.Exceptions.BadRequestException(ApplicationErrors.UNKNOWN_MFA_METHOD.ToErrorCode(),
                                                                                       "Method for MFA not supported");
                    }
            }
        }

        /// <summary>
        /// Resets the MFA info for the given user.
        /// </summary>
        /// <param name="userId">Id of the user to reset the MFA for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operation.</returns>
        public async Task ResetMFAMethodAsync(Guid userId)
        {
            await _usersRepository.CheckUserByIdAsync(userId);

            var mfaHelper = new MFAHelper(this);
            await mfaHelper.ResetTFAAsync(userId);
        }

        /// <summary>
        /// Gets the info about MFA for the given user.
        /// </summary>
        /// <param name="request">The request with the parameters to get the info.</param>
        /// <returns>A <see cref="Task{GetUserMFAResponse}"/> representing the asynchronous operation.</returns>
        public async Task<GetUserMFAResponse> GetUserMFAInfoAsync(GetUserMFARequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            await _usersRepository.CheckUserByIdAsync(request.UserId);

            var mfaHelper = new MFAHelper(this);

            var mfaMethod = await GetAttributeValueAsync(request.UserId, UserAttributeKeys.MFAMethod, string.Empty);
            if (string.IsNullOrEmpty(mfaMethod))
            {
                throw new BadRequestException("Method for MFA is not set");
            }

            GetUserMFAResponse result;

            switch (mfaMethod.ToLower())
            {
                case "authenticator":
                    {
                        result = new GetUserMFAResponse
                        {
                            UserId = request.UserId,
                            MFAMethod = mfaMethod,
                            MFAIsEnabled = await mfaHelper.IsMFAEnabledAsync(request.UserId),
                        };

                        // If MFA is not enabled yet, we need to generate a secret and url for pairing an authenticator.
                        if (!result.MFAIsEnabled)
                        {
                            result.MFAAuthenticatorQRUrl = await mfaHelper.GetAuthenticatorUriAsync(request.UserId);
                            result.MFAAuthenticatorSecret = await mfaHelper.GetAuthenticatorSecretAsync(request.UserId);
                        }

                        break;
                    }

                case "email-code":
                    {
                        var key = $"security.mfa.emailcode.expiration-in-seconds";
                        int expiresIn = 300;
                        expiresIn = await _configurationManager.GetConfigurationAsync<int>(key, expiresIn);

                        result = new GetUserMFAResponse
                        {
                            UserId = request.UserId,
                            MFAMethod = mfaMethod,
                            MFAEmailCode = mfaHelper.GenerateEmailCode(),
                            MFAEmailCodeExpiresIn = expiresIn,
                            MFAEmailCodeExpiresAt = DateTime.UtcNow.AddSeconds(expiresIn)
                        };

                        // Store the email code and the expiration date/time
                        await SetAttributeValueAsync(request.UserId, UserAttributeKeys.MFAEmailCode, result.MFAEmailCode);
                        await SetAttributeValueAsync(request.UserId, UserAttributeKeys.MFAEmailCodeExpiration, XmlConvert.ToString(result.MFAEmailCodeExpiresAt.Value, XmlDateTimeSerializationMode.Utc));

                        await _masterClientUsersRepository.SaveChangesAsync();

                        break;
                    }

                default:
                    {
                        throw new NetProGroup.Framework.Exceptions.APIException("Method for MFA not supported");
                    }
            }

            return result;
        }

        /// <summary>
        /// Verifies the entered code for MFA.
        /// </summary>
        /// <param name="request">The request holding all parameters.</param>
        /// <returns>The response of the verification.</returns>
        public async Task<VerifyUserMFAResponse> VerifyUserMFAResponseAsync(VerifyUserMFARequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            await _usersRepository.CheckUserByIdAsync(request.UserId);

            var result = new VerifyUserMFAResponse
            {
                UserId = request.UserId
            };

            var mfaHelper = new MFAHelper(this);

            var mfaMethod = await GetAttributeValueAsync(request.UserId, UserAttributeKeys.MFAMethod, string.Empty);
            if (string.IsNullOrEmpty(mfaMethod))
            {
                throw new BadRequestException("Method for MFA is not set");
            }

            switch (mfaMethod.ToLower())
            {
                case "authenticator":
                    {
                        result.Success = await mfaHelper.VerifyCodeAsync(request.UserId, request.ResponseCode);

                        break;
                    }

                case "email-code":
                    {
                        // Store the email code and the expiration date/time
                        var emailCode = await GetAttributeValueAsync(request.UserId, UserAttributeKeys.MFAEmailCode);
                        var expiration = await GetAttributeValueAsync(request.UserId, UserAttributeKeys.MFAEmailCodeExpiration);
                        if (!string.IsNullOrEmpty(emailCode) && !string.IsNullOrEmpty(expiration))
                        {
                            if (emailCode == request.ResponseCode)
                            {
                                var expirationDateTime = XmlConvert.ToDateTime(expiration, XmlDateTimeSerializationMode.Utc);
                                if (expirationDateTime > DateTime.UtcNow)
                                {
                                    result.Success = true;
                                }
                            }
                        }

                        break;
                    }

                default:
                    {
                        throw new NetProGroup.Framework.Exceptions.APIException("Method for MFA not supported");
                    }
            }

            return result;
        }

        /// <summary>
        /// Gets the value of an attribute for the given user.
        /// </summary>
        /// <param name="userId">Id of the user to get the attribute for.</param>
        /// <param name="key">Key of the attribute to get.</param>
        /// <param name="default">Option default value to return if attribute does not exist.</param>
        /// <returns>The value of the attribute or the default as a string.</returns>
        public async Task<string> GetAttributeValueAsync(Guid userId, string key, string @default = null)
        {
            var attr = (await GetUserAttributesAsync(userId)).FirstOrDefault(x => x.Key.Equals(key, StringComparison.OrdinalIgnoreCase));
            if (attr == null)
            {
                return @default;
            }

            return attr.Value;
        }

        /// <summary>
        /// Gets the value of an attribute for the given user.
        /// </summary>
        /// <typeparam name="TPropType">The type of the value to return.</typeparam>
        /// <param name="userId">Id of the user to get the attribute for.</param>
        /// <param name="key">Key of the attribute to get.</param>
        /// <param name="default">Option default value to return if attribute does not exist.</param>
        /// <returns>The value of the attribute or the default as TPropType.</returns>
        public async Task<TPropType> GetAttributeValueAsync<TPropType>(Guid userId, string key, TPropType @default = default(TPropType))
        {
            var attr = (await GetUserAttributesAsync(userId)).FirstOrDefault(x => x.Key.Equals(key, StringComparison.OrdinalIgnoreCase));
            if (attr == null)
            {
                return @default;
            }

            return Converter.To<TPropType>(attr.Value);
        }

        /// <inheritdoc/>
        public async Task SetAttributeValueAsync(Guid userId, string key, string value, bool saveChanges = false)
        {
            var attr = (await GetUserAttributesAsync(userId)).FirstOrDefault(x => x.Key.Equals(key, StringComparison.OrdinalIgnoreCase));
            if (attr != null)
            {
                if (value == null)
                {
                    await _userAttributesRepository.DeleteAsync(attr, false);
                }
                else
                {
                    attr.Value = value;
                    await _userAttributesRepository.UpdateAsync(attr, false);
                }
            }
            else
            {
                attr = new UserAttribute { UserId = userId, Key = key, Value = value };
                await _userAttributesRepository.InsertAsync(attr, false);

                _cache[userId].Add(attr);
            }

            if (saveChanges)
            {
                await _userAttributesRepository.SaveChangesAsync();
            }
        }

        /// <inheritdoc/>
        public async Task SetAttributeValueAsync<TPropType>(Guid userId, string key, TPropType value, bool saveChanges = false)
        {
            var attr = (await GetUserAttributesAsync(userId)).FirstOrDefault(x => x.Key.Equals(key, StringComparison.OrdinalIgnoreCase));
            if (attr != null)
            {
                if (value == null)
                {
                    await _userAttributesRepository.DeleteAsync(attr, false);
                }
                else
                {
                    attr.Value = Converter.To<string>(value);
                }
            }
            else
            {
                attr = new UserAttribute { UserId = userId, Key = key, Value = Converter.To<string>(value) };
                await _userAttributesRepository.InsertAsync(attr, false);

                _cache[userId].Add(attr);
            }

            if (saveChanges)
            {
                await _userAttributesRepository.SaveChangesAsync();
            }
        }

        /// <inheritdoc/>
        public async Task<ApplicationUserDTO> UpdateUserObjectIdAsync(Guid userId, Guid objectId)
        {
            Check.NotDefaultOrNull<Guid>(userId, nameof(userId));
            Check.NotDefaultOrNull<Guid>(objectId, nameof(objectId));

            var user = await _usersRepository.CheckUserByIdAsync(userId);
            if (user.ObjectId == null)
            {
                user.ObjectId = objectId;
                await _usersRepository.UpdateUserAsync(user);
            }

            return await _userManager.GetUserByIdAsync(user.Id);
        }

        /// <inheritdoc/>
        public async Task<IPagedList<ListMasterClientUserDTO>> ListMasterClientUsersAsync(ListUsersRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var masterClientUsers = await _masterClientUsersRepository.FindByConditionAsPagedListAsync(mcu => mcu.MasterClientId == request.MasterClientId.Value,
                                                                                                       pageNumber: request.PagingInfo.PageNumber,
                                                                                                       pageSize: request.PagingInfo.PageSize,
                                                                                                       q => ApplySorting(q, request.SortingInfo).Include(mcu => mcu.User));

            var items = _mapper.Map<List<ListMasterClientUserDTO>>(masterClientUsers);
            await UpdateInvitationDetailsAsync(items);

            return new StaticPagedList<ListMasterClientUserDTO>(items, masterClientUsers.GetMetaData());
        }

        /// <inheritdoc />
        public async Task<IPagedList<(Guid MasterClientId, List<ListUserDTO> MasterClientUserItems)>> ListMasterClientUsersByMasterClientAsync(ListUsersByMasterClientRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            var masterClientUsers = await _masterClientUsersRepository.FindByConditionAsPagedListAsync(
                mcu => request.MasterClientIds.Contains(mcu.MasterClientId) &&
                       (mcu.UserId != UserConsts.SystemUserId && mcu.UserId != UserConsts.InboxUserId),
                pageNumber: request.PagingInfo.PageNumber,
                pageSize: request.PagingInfo.PageSize,
                q => q.Include(mcu => mcu.User)
                      .TagWithCallSite());

            Dictionary<Guid, UserAttribute> attributesByUser = await GetAttributesForUsers(masterClientUsers.Select(mcu => mcu.UserId));
            var items = masterClientUsers.GroupBy(user => user.MasterClientId, (guid, users) =>
            (
                MasterClientId: guid,
                MasterClientUserItems: users.Select(user =>
                {
                    var dto = _mapper.Map<ListUserDTO>(user.User);
                    UpdateInvitationDetails(attributesByUser, dto);
                    return dto;
                }).ToList()));

            return new StaticPagedList<(Guid MasterClientId, List<ListUserDTO> MasterClientUserItems)>(items, masterClientUsers.GetMetaData());
        }

        /// <inheritdoc/>
        public async Task<MFAResetResponse> RequestMFAResetAsync(MFAResetRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            await _usersRepository.CheckUserByIdAsync(request.UserId);

            var mfaHelper = new MFAHelper(this);

            var key = $"security.mfa-reset.emailcode.expiration-in-seconds";
            int expiresIn = 300;
            expiresIn = await _configurationManager.GetConfigurationAsync<int>(key, expiresIn);

            var result = new MFAResetResponse
            {
                UserId = request.UserId,
                MFAEmailCode = mfaHelper.GenerateEmailCode(),
                MFAEmailCodeExpiresIn = expiresIn,
                MFAEmailCodeExpiresAt = DateTime.UtcNow.AddSeconds(expiresIn)
            };

            // Store the email code and the expiration date/time
            await SetAttributeValueAsync(request.UserId, UserAttributeKeys.MFAResetEmailCode, result.MFAEmailCode);
            await SetAttributeValueAsync(request.UserId, UserAttributeKeys.MFAResetEmailCodeExpiration, XmlConvert.ToString(result.MFAEmailCodeExpiresAt.Value, XmlDateTimeSerializationMode.Utc));

            await _masterClientUsersRepository.SaveChangesAsync();

            return result;
        }

        /// <inheritdoc/>
        public async Task<ConfirmMFAResetResponse> ConfirmMFAResetAsync(ConfirmMFAResetRequest request)
        {
            ArgumentNullException.ThrowIfNull(request, nameof(request));

            await _usersRepository.CheckUserByIdAsync(request.UserId);

            var result = new ConfirmMFAResetResponse
            {
                UserId = request.UserId
            };

            // Store the email code and the expiration date/time
            var emailCode = await GetAttributeValueAsync(request.UserId, UserAttributeKeys.MFAResetEmailCode);
            var expiration = await GetAttributeValueAsync(request.UserId, UserAttributeKeys.MFAResetEmailCodeExpiration);
            if (!string.IsNullOrEmpty(emailCode) && !string.IsNullOrEmpty(expiration))
            {
                if (emailCode == request.ResponseCode)
                {
                    var expirationDateTime = XmlConvert.ToDateTime(expiration, XmlDateTimeSerializationMode.Utc);
                    if (expirationDateTime > DateTime.UtcNow)
                    {
                        result.Success = true;

                        var mfaHelper = new MFAHelper(this);
                        await mfaHelper.ResetTFAAsync(request.UserId);
                    }
                }
            }

            return result;
        }

        /// <summary>
        /// Gets the user by email that represents a client user.
        /// </summary>
        /// <param name="emailAddress">The email adress to fidn the user for.</param>
        /// <returns>A <see cref="Task"/> representing the asynchronous operationng the found user.</returns>
        public async Task<ApplicationUser> GetClientUserByEmailAsync(string emailAddress)
        {
            var users = await _usersRepository.SearchAsync(filter: emailAddress);

            ApplicationUser result = null;
            foreach (var user in users.Where(u => u.Email != null && u.Email.Equals(emailAddress, StringComparison.OrdinalIgnoreCase)))
            {
                if (user.ApplicationUserRoles.Any(aur => aur.RoleId == WellKnownRoleIds.Client))
                {
                    if (result != null)
                    {
                        throw new ConflictException($"Found multiple client users with same emailaddress '{emailAddress}'");
                    }

                    result = user;
                }
            }

            return result;
        }


        /// <summary>
        /// Gets the complete list of attributes for the given user. Uses caching in current instance.
        /// </summary>
        /// <param name="userId">Id of the user to get the attributes for.</param>
        /// <returns>Collection of attributes.</returns>
        private async Task<ICollection<UserAttribute>> GetUserAttributesAsync(Guid userId)
        {
            if (!_cache.TryGetValue(userId, out List<UserAttribute> value))
            {
                value = new List<UserAttribute>();
                _cache.Add(userId, value);
                _cache[userId].AddRange(await _userAttributesRepository.FindByConditionAsync(x => x.UserId == userId));
            }

            return value;
        }

        private static void UpdateInvitationDetails(Dictionary<Guid, UserAttribute> attributesByUser, ListUserDTO userDTO)
        {
            ArgumentNullException.ThrowIfNull(attributesByUser, nameof(attributesByUser));
            ArgumentNullException.ThrowIfNull(userDTO, nameof(userDTO));

            if (attributesByUser.TryGetValue(userDTO.Id, out UserAttribute invitationAttribute))
            {
                userDTO.InvitationDetails = new UserInvitationDetailsDTO
                {
                    IsInvited = true,
                    LastInvitationAt = Converter.To<DateTime>(invitationAttribute.Value)
                };
            }
            else
            {
                userDTO.InvitationDetails = new UserInvitationDetailsDTO
                {
                    IsInvited = false,
                    LastInvitationAt = null
                };
            }
        }

        private async Task UpdateInvitationDetailsAsync(IList<ListUserDTO> userDTOs)
        {
            Dictionary<Guid, UserAttribute> attributesByUser = await GetAttributesForUsers(userDTOs.Select(mcu => mcu.Id));

            foreach (var userDTO in userDTOs)
            {
                UpdateInvitationDetails(attributesByUser, userDTO);
            }
        }

        private async Task UpdateInvitationDetailsAsync(IList<ListMasterClientUserDTO> userDTOs)
        {
            Dictionary<Guid, UserAttribute> attributesByUser = await GetAttributesForUsers(userDTOs.Select(mcu => mcu.Id));

            foreach (var userDTO in userDTOs)
            {
                UpdateInvitationDetails(attributesByUser, userDTO);
            }
        }

        private async Task<Dictionary<Guid, UserAttribute>> GetAttributesForUsers(IEnumerable<Guid> userIds)
        {
            // Get the invitation data for the users
            var attributes = await _userAttributesRepository.FindByConditionAsync(ua => userIds.Contains(ua.UserId) && ua.Key == UserAttributeKeys.Invitation_Last_Sent);
            var attributesByUser = attributes.ToDictionary(a => a.UserId);
            return attributesByUser;
        }

        private static IQueryable<MasterClientUser> ApplySorting(IQueryable<MasterClientUser> query, SortingInfo sortingInfo)
        {
            sortingInfo = sortingInfo.Validate();

            // TODO convert to SortableColumns sorting
            var q = sortingInfo.SortBy.ToLower() switch
            {
                "lastname" => sortingInfo.SortOrder == OrderByDefines.Ascending ? query.OrderBy(user => user.User.Surname) : query.OrderByDescending(user => user.User.Surname),
                "firstname" => sortingInfo.SortOrder == OrderByDefines.Ascending ? query.OrderBy(user => user.User.Name) : query.OrderByDescending(user => user.User.Name),
                "email" => sortingInfo.SortOrder == OrderByDefines.Ascending ? query.OrderBy(user => user.User.Email) : query.OrderByDescending(user => user.User.Email),
                _ => sortingInfo.SortOrder == OrderByDefines.Ascending ? query.OrderBy(mcu => mcu.User.Surname) : query.OrderByDescending(mcu => mcu.User.Surname),
            };
            return q;
        }

        /// <summary>
        /// Applies sorting based on the request parameters.
        /// </summary>
        private static IQueryable<ListApplicationUsersDTO> ApplySorting(IQueryable<ListApplicationUsersDTO> query, UsersRequestDTO request)
        {
            var overrides = new Dictionary<string, Expression<Func<ListApplicationUsersDTO, object>>>();

            Expression<Func<ListApplicationUsersDTO, object>> defaultSort = u => u.Email;

            return query.SortBySpecification<ListApplicationUsersDTO, UsersRequestDTO>(request.ToSortingInfo(), overrides, defaultSort);
        }

        /// <summary>
        /// Gets the predicate expression based on the provided filter criteria.
        /// </summary>
        /// <param name="filter">The filter criteria.</param>
        /// <returns>The predicate expression.</returns>
        private static Expression<Func<ApplicationUser, bool>> GetApplicationUserPredicate(string filter)
        {
            Expression<Func<ApplicationUser, bool>> predicate = legalEntity => true;

            predicate = predicate.And(user => user.Id != UserConsts.SystemUserId && user.Id != UserConsts.InboxUserId);

            if (!string.IsNullOrEmpty(filter))
            {
                predicate = predicate.And(user => user.UserName.Contains(filter) ||
                                                  (user.Email != null && user.Email.Contains(filter)) ||
                                                  (user.Name != null && user.Name.Contains(filter)) ||
                                                  (user.Surname != null && user.Surname.Contains(filter)));
            }

            return predicate;
        }

        private IQueryable<UserDto> GetUsersWithRolesQuery(Expression<Func<ApplicationUser, bool>> getApplicationUserPredicate)
        {
            var roles = _applicationUsersRepository.DbContext.Set<ApplicationRole>().AsQueryable();

            var usersWithRoles = _applicationUsersRepository.DbContext.Set<ApplicationUser>()
                                                            .Where(getApplicationUserPredicate)
                                                            .Select(user => new UserDto
                                                            {
                                                                User = user,
                                                                RoleNames = user.ApplicationUserRoles.Select(aur => roles.SingleOrDefault(r => r.Id == aur.RoleId)).Select(role => role.Name)
                                                            });
            return usersWithRoles;
        }

        /// <summary>
        /// Intermediate class to support Automapper Projection.
        /// This is needed because there is no EF navigation property to the roles where we can get the role names.
        /// So we first join the user and its roles in this class, and then we can project to the DTOs we need.
        /// </summary>
        internal class UserDto
        {
            /// <summary>
            /// Gets or sets the user.
            /// </summary>
            public ApplicationUser User { get; set; }

            /// <summary>
            /// Gets or sets the roles of the user.
            /// </summary>
            public IEnumerable<string> RoleNames { get; set; }
        }
    }
}
