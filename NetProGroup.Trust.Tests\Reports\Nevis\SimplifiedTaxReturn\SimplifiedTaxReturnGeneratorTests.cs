﻿using ClosedXML.Excel;
using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using NetProGroup.Trust.Application.Contracts.Reports;
using NetProGroup.Trust.Application.Contracts.Submissions;
using NetProGroup.Trust.DataManager.Submissions;
using NetProGroup.Trust.Domain.LegalEntities;
using NetProGroup.Trust.Domain.Shared.Consts;
using NetProGroup.Trust.Domain.Shared.Settings;
using NetProGroup.Trust.DomainShared.Enums;
using NetProGroup.Trust.Tests.Shared;

namespace NetProGroup.Trust.Tests.Reports.Nevis.SimplifiedTaxReturn
{
    [TestFixture()]
    public class SimplifiedTaxReturnGeneratorTests : TestBase
    {
        /// <summary>
        /// Tests that the email is correctly retrieved from the submission attributes
        /// and populated in the Excel report.
        /// </summary>
        [Test]
        [Ignore("This is fixed in develop, unignore when merged")]
        public async Task CreatedByEmail_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);
            var testEmail = ClientUser.Email;
            var oldEmail = "<EMAIL>";

            // Arrange
            await CreateSubmission(
                dataSet: new Dictionary<string, string> { { FormKeys.ContactEmail, oldEmail } }
            );

            // Act & Assert
            await AssertCellValue(1, testEmail,
                $"Cell (2, 1) should contain the email '{testEmail}' from the submission attributes, not '{oldEmail}' from the form document attributes");
        }

        /// <summary>
        /// Tests that the company name is correctly populated in the Excel report.
        /// </summary>
        [Test]
        public async Task CompanyName_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);
            var companyName = "Test Company Name";

            // Arrange
            await CreateSubmission(
                legalEntitySetup: entity => entity.Name = companyName
            );

            // Act & Assert
            await AssertCellValue(2, companyName, $"Cell (2, 2) should contain the company name '{companyName}'");
        }

        /// <summary>
        /// Tests that the company entity number is correctly populated in the Excel report.
        /// When LegacyCode is available, it should be used.
        /// </summary>
        [Test]
        public async Task CompanyEntityNumber_WithLegacyCode_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);
            var legacyCode = "LEGACY_CODE_123";
            var code = "CODE_123";

            // Arrange
            await CreateSubmission(
                legalEntitySetup: entity =>
                {
                    entity.Code = code;
                    entity.LegacyCode = legacyCode;
                }
            );

            // Act & Assert
            await AssertCellValue(3, legacyCode,
                $"Cell (2, 3) should contain the legacy code '{legacyCode}' when it's available");
        }

        /// <summary>
        /// Tests that the company entity number is correctly populated in the Excel report.
        /// When LegacyCode is not available, Code should be used.
        /// </summary>
        [Test]
        public async Task CompanyEntityNumber_WithoutLegacyCode_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);
            var code = "CODE_123";

            // Arrange
            await CreateSubmission(
                legalEntitySetup: entity =>
                {
                    entity.Code = code;
                    entity.LegacyCode = null;
                }
            );

            // Act & Assert
            await AssertCellValue(3, code, $"Cell (2, 3) should contain the code '{code}' when legacy code is not available");
        }

        /// <summary>
        /// Tests that the master client code is correctly populated in the Excel report.
        /// </summary>
        [Test]
        public async Task MasterClientCode_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);

            // We need to use the existing master client code since it's not easy to mock
            var masterClientCode = _masterClient.Code;

            // Arrange
            await CreateSubmission();

            // Act & Assert
            await AssertCellValue(4, masterClientCode, $"Cell (2, 4) should contain the master client code '{masterClientCode}'");
        }

        /// <summary>
        /// Tests that the company number (incorporation number) is correctly populated in the Excel report.
        /// </summary>
        [Test]
        public async Task CompanyNumber_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);
            var incorporationNr = "INCORP_12345";

            // Arrange
            await CreateSubmission(
                legalEntitySetup: entity => entity.IncorporationNr = incorporationNr
            );

            // Act & Assert
            await AssertCellValue(5, incorporationNr, $"Cell (2, 5) should contain the incorporation number '{incorporationNr}'");
        }

        /// <summary>
        /// Tests that the VP number (code) is correctly populated in the Excel report.
        /// </summary>
        [Test]
        public async Task VPNumber_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);
            var code = "VP_CODE_123";

            // Arrange
            await CreateSubmission(
                legalEntitySetup: entity => entity.Code = code
            );

            // Act & Assert
            await AssertCellValue(6, code, $"Cell (2, 6) should contain the VP number (code) '{code}'");
        }

        /// <summary>
        /// Tests that the submission status is correctly populated as "PAID" when IsPaid is true.
        /// </summary>
        [Test]
        public async Task SubmissionStatus_WhenPaid_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);

            // Arrange
            await CreateSubmission(markSubmissionAsPaid: true);

            // Act & Assert
            await AssertCellValue(7, "PAID", "Cell (2, 7) should contain 'PAID' when IsPaid is true");
        }

        /// <summary>
        /// Tests that the submission status is correctly populated with the status string when IsPaid is false.
        /// </summary>
        [Test]
        public async Task SubmissionStatus_WhenNotPaid_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);
            var status = SubmissionStatus.Submitted;

            // Arrange
            await CreateSubmission(markSubmissionAsPaid: false);

            // Act & Assert
            await AssertCellValue(7, status.ToString(), $"Cell (2, 7) should contain '{status}' when IsPaid is false");
        }

        /// <summary>
        /// Tests that the creation date is correctly formatted and populated in the Excel report.
        /// </summary>
        [Test]
        public async Task CreationDate_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);
            var creationDate = DateTime.UtcNow;
            var expectedDateString = creationDate.ToString("yyyy-MM-dd");

            // Arrange
            await CreateSubmission();

            // Act & Assert
            await AssertCellValue(8, expectedDateString, $"Cell (2, 8) should contain the creation date '{expectedDateString}'");
        }

        /// <summary>
        /// Tests that the submitted date is correctly formatted and populated in the Excel report.
        /// </summary>
        [Test]
        public async Task SubmittedDate_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);
            var submittedDate = DateTime.UtcNow;
            var expectedDateString = submittedDate.ToString("yyyy-MM-dd");

            // Arrange
            await CreateSubmission();

            // Act & Assert
            await AssertCellValue(9, expectedDateString, $"Cell (2, 9) should contain the submitted date '{expectedDateString}'");
        }

        /// <summary>
        /// Tests that the payment date is correctly formatted and populated in the Excel report.
        /// </summary>
        [Test]
        public async Task PaymentDate_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);
            var paymentDate = DateTime.UtcNow;
            var expectedDateString = paymentDate.ToString("yyyy-MM-dd");

            // Arrange
            await CreateSubmission();

            // Act & Assert
            await AssertCellValue(10, expectedDateString, $"Cell (2, 10) should contain the payment date '{expectedDateString}'");
        }

        /// <summary>
        /// Tests that the payment reference is correctly populated in the Excel report.
        /// </summary>
        [Test]
        public async Task PaymentReference_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);
            var paymentReference = $"TEST_123-TEST_LEGAL_ENTITY-{DateTime.Now:yyyyMMdd}";

            // Arrange
            await CreateSubmission();

            // Act & Assert
            await AssertCellValue(11, paymentReference,
                $"Cell (2, 11) should contain the payment reference '{paymentReference}'");
        }

        /// <summary>
        /// Tests that the financial year is correctly populated in the Excel report.
        /// </summary>
        [Test]
        public async Task FinancialYear_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);
            var financialYear = 2022;

            // Arrange
            await CreateSubmission(
                submissionDTOSetup: dto =>
                    dto.FinancialYear = financialYear
            );

            // Act & Assert
            await AssertCellValue(12, financialYear.ToString(),
                $"Cell (2, 12) should contain the financial year '{financialYear}'");
        }

        /// <summary>
        /// Tests that the referral office is correctly populated in the Excel report.
        /// </summary>
        [Test]
        public async Task ReferralOffice_IsCorrectlyPopulatedInReport()
        {
            SetWorkContextUser(ClientUser);
            var referralOffice = "Test Referral Office";

            // Arrange
            await CreateSubmission(
                legalEntitySetup: entity => entity.ReferralOffice = referralOffice
            );

            // Act & Assert
            await AssertCellValue(13, referralOffice, $"Cell (2, 13) should contain the referral office '{referralOffice}'");
        }

        /// <summary>
        /// Creates a submission for testing with customizable properties.
        /// </summary>
        /// <param name="legalEntitySetup">Optional action to customize the legal entity.</param>
        /// <param name="submissionDTOSetup">Optional action to customize the submission DTO.</param>
        /// <param name="dataSet">Optional dataset to update the submission with.</param>
        /// <param name="submissionSetup">Optional action to perform additional setup on the submission after creation.</param>
        /// <param name="markSubmissionAsPaid">Whether to mark the submission as paid.</param>
        /// <returns>The created submission.</returns>
        protected async Task<SubmissionDTO> CreateSubmission(
            Action<LegalEntity> legalEntitySetup = null,
            Action<StartSubmissionDTO> submissionDTOSetup = null,
            Dictionary<string, string> dataSet = null,
            Func<ISubmissionsManager, SubmissionDTO, Task> submissionSetup = null,
            bool markSubmissionAsPaid = true)
        {
            // Create a legal entity
            var legalEntity = new LegalEntity()
            {
                Name = "Test Legal Entity",
                Code = "TEST_LEGAL_ENTITY",
                JurisdictionId = JurisdictionNevisId,
                MasterClientId = _masterClient.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                EntityTypeCode = LegalEntityTypes.IBC,
                EntityType = LegalEntityType.Company,
                EntityStatus = LegalEntityStatusNames.Active,
                ExternalUniqueId = "asdf",
                IncorporationNr = "1234",
                LegacyCode = "1234",
                IncorporationDate = DateTime.UtcNow.AddYears(-2),
                EntityTypeName = "IBC",
                ReferralOffice = "Test Office"
            };

            // Apply custom setup to the legal entity if provided
            legalEntitySetup?.Invoke(legalEntity);

            // Insert the legal entity
            legalEntity = await _server.Services.GetRequiredService<ILegalEntitiesRepository>().InsertAsync(legalEntity, true);

            // Create a submission
            var submissionsManager = _server.Services.GetService<ISubmissionsManager>();

            // Create a default submission DTO
            var startSubmissionDTO = new StartSubmissionDTO()
            {
                FinancialYear = 2019,
                ModuleId = ModuleStrId,
                LegalEntityId = legalEntity.Id
            };

            // Apply custom setup to the submission DTO if provided
            submissionDTOSetup?.Invoke(startSubmissionDTO);

            // Ensure LegalEntityId is set
            if (startSubmissionDTO.LegalEntityId == Guid.Empty)
            {
                startSubmissionDTO.LegalEntityId = legalEntity.Id;
            }

            var submission = await submissionsManager.StartSubmissionAsync(startSubmissionDTO);

            // Update submission dataset if provided
            if (dataSet != null)
            {
                await submissionsManager.UpdateSubmissionDataSetAsync(new SubmissionDataSetDTO()
                {
                    Id = submission.Id, DataSet = dataSet
                });
            }

            // Apply custom setup to the submission if provided
            if (submissionSetup != null)
            {
                await submissionSetup(submissionsManager, submission);
            }

            // Submit the submission
            await submissionsManager.SubmitSubmissionAsync(new SubmitSubmissionDTO() { SubmissionId = submission.Id });

            if (markSubmissionAsPaid)
            {
                await submissionsManager.MarkSubmissionsAsPaidAsync([submission.Id], true, [JurisdictionNevisId]);
            }

            return submission;
        }

        /// <summary>
        /// Helper method to generate the Nevis report.
        /// </summary>
        /// <returns>The generated report.</returns>
        private async Task<ReportDownloadResponseDTO> GenerateReport()
        {
            // Create a generator with the mocked template provider
            var submissionReportsAppService = _server.Services.GetRequiredService<ISubmissionReportsAppService>();

            SetWorkContextUser(ManagementUser);

            // Generate the report
            return await submissionReportsAppService.GenerateSubmissionsReportForNevisAsync(new SubmissionsReportRequestNevisDTO()
            {
                ModuleId = ModuleStrId
            });
        }

        /// <summary>
        /// Helper method to assert a cell value in the Excel report.
        /// </summary>
        /// <param name="columnIndex">The column index (1-based).</param>
        /// <param name="expectedValue">The expected value.</param>
        /// <param name="message">The assertion message.</param>
        /// <returns>A task representing the asynchronous operation.</returns>
        private async Task AssertCellValue(int columnIndex, string expectedValue, string message)
        {
            // Generate the report
            var result = await GenerateReport();
            result.Should().NotBeNull();

            // Read the generated Excel file to verify the content
            using (var resultWorkbook = new XLWorkbook(result.FileContent))
            {
                var worksheet = resultWorkbook.Worksheet(1);
                var cellValue = worksheet.Cell(2, columnIndex).Value.ToString();

                // Verify that the value is correctly populated
                cellValue.Should().Be(expectedValue, message);
            }
        }
    }
}