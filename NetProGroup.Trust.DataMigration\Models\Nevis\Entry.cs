using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Nevis
{
    /// <summary>
    /// Represents an entry in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class Entry : MongoEntityWithId
    {
        /// <summary>
        /// Gets or sets the legacy company code.
        /// </summary>
        [BsonElement("company")]
        public string Company { get; set; }

        /// <summary>
        /// Gets or sets the company code.
        /// </summary>
        [BsonElement("code")]
        public string Code { get; set; }

        /// <summary>
        /// Gets or sets the status of the entry (e.g., "SUBMITTED", "PAID").
        /// </summary>
        [BsonElement("status")]
        public string Status { get; set; }

        /// <summary>
        /// Gets or sets the period year for the entry.
        /// </summary>
        [BsonElement("period_year")]
        public string PeriodYear { get; set; }

        /// <summary>
        /// Gets or sets a value indicating whether to use new branding.
        /// </summary>
        [BsonElement("use_new_branding")]
        public bool? UseNewBranding { get; set; }

        /// <summary>
        /// Gets or sets the version of the entry.
        /// </summary>
        [BsonElement("version")]
        public string Version { get; set; }

        /// <summary>
        /// Gets or sets the creation date of the entry.
        /// </summary>
        [BsonElement("createdAt")]
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Gets or sets the last updated date of the entry.
        /// </summary>
        [BsonElement("updatedAt")]
        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// Gets or sets the user who created the entry.
        /// </summary>
        [BsonElement("created_by")]
        public string CreatedBy { get; set; }

        /// <summary>
        /// Gets or sets the user who submitted the entry.
        /// </summary>
        [BsonElement("submitted_by")]
        public string SubmittedBy { get; set; }

        /// <summary>
        /// Gets or sets the submission date of the entry.
        /// </summary>
        [BsonElement("submitted_at")]
        public DateTime? SubmittedAt { get; set; }

        /// <summary>
        /// Gets or sets the export date of the entry.
        /// </summary>
        [BsonElement("exported_at")]
        public DateTime? ExportedAt { get; set; }

        /// <summary>
        /// Gets or sets the user who exported the entry.
        /// </summary>
        [BsonElement("exported_by")]
        public string ExportedBy { get; set; }

        /// <summary>
        /// Gets or sets the amount.
        /// </summary>
        [BsonElement("amount")]
        public decimal Amount { get; set; }

        /// <summary>
        /// Gets or sets the invoice number.
        /// </summary>
        [BsonElement("invoice_number")]
        public string InvoiceNumber { get; set; }

        /// <summary>
        /// Gets or sets the invoice export date.
        /// </summary>
        [BsonElement("invoice_export_date")]
        public DateTime? InvoiceExportDate { get; set; }

        /// <summary>
        /// Gets or sets the company data.
        /// </summary>
        [BsonElement("company_data")]
        public CompanyData CompanyData { get; set; }

        /// <summary>
        /// Gets or sets the address information.
        /// </summary>
        [BsonElement("address")]
        public Address Address { get; set; }

        /// <summary>
        /// Gets or sets the client company information.
        /// </summary>
        [BsonElement("client_company")]
        public ClientCompany ClientCompany { get; set; }

        /// <summary>
        /// Gets or sets the company representative information.
        /// </summary>
        [BsonElement("company_representative")]
        public CompanyRepresentative CompanyRepresentative { get; set; }

        /// <summary>
        /// Gets or sets the corporate business information.
        /// </summary>
        [BsonElement("corporate_business")]
        public CorporateBusiness CorporateBusiness { get; set; }

        /// <summary>
        /// Gets or sets the intellectual property acquired information.
        /// </summary>
        [BsonElement("intellectual_property_acquired")]
        public IntellectualPropertyAcquired IntellectualPropertyAcquired { get; set; }

        /// <summary>
        /// Gets or sets the financial report information.
        /// </summary>
        [BsonElement("financial_report")]
        public FinancialReport FinancialReport { get; set; }

        /// <summary>
        /// Gets or sets the corporation related information.
        /// </summary>
        [BsonElement("corporation_related")]
        public CorporationRelated CorporationRelated { get; set; }

        /// <summary>
        /// Gets or sets the declaration certificate information.
        /// </summary>
        [BsonElement("declaration_certificate")]
        public DeclarationCertificate DeclarationCertificate { get; set; }

        /// <summary>
        /// Gets or sets the corporate address information.
        /// </summary>
        [BsonElement("corporate_address")]
        public CorporateAddress CorporateAddress { get; set; }

        /// <summary>
        /// Gets or sets the list of business activities.
        /// </summary>
        [BsonElement("business_activities")]
        public List<BusinessActivity> BusinessActivities { get; set; }

        /// <summary>
        /// Gets or sets the confirmation information.
        /// </summary>
        [BsonElement("confirmation")]
        public ConfirmationSchema Confirmation { get; set; }

        /// <summary>
        /// Gets or sets the late payment fees information.
        /// </summary>
        [BsonElement("latePaymentFees")]
        public LatePaymentFeesSchema LatePaymentFees { get; set; }

        /// <summary>
        /// Gets or sets the payment information.
        /// </summary>
        [BsonElement("payment")]
        public PaymentSchema Payment { get; set; }

        /// <summary>
        /// Gets or sets the reopened information.
        /// </summary>
        [BsonElement("reopened")]
        public ReopenedSchema Reopened { get; set; }
    }
}
