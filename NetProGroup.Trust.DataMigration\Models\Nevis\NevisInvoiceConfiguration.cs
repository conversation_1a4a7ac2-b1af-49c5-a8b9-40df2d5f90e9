using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Nevis
{
    /// <summary>
    /// Represents a Nevis invoice configuration in the old database.
    /// This class contains settings for invoice numbering and late payment fees.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class NevisInvoiceConfiguration : MongoEntityWithId
    {
        /// <summary>
        /// Gets or sets the initial range number for invoice numbering.
        /// </summary>
        [BsonElement("initialRangeNumber")]
        public int InitialRangeNumber { get; set; }

        /// <summary>
        /// Gets or sets the current available number for invoice numbering.
        /// </summary>
        [BsonElement("currentAvailableNumber")]
        public int CurrentAvailableNumber { get; set; }

        /// <summary>
        /// Gets or sets the current year.
        /// </summary>
        [BsonElement("currentYear")]
        public DateTime CurrentYear { get; set; }

        /// <summary>
        /// Gets or sets the current month (1-12).
        /// </summary>
        [BsonElement("currentMonth")]
        public int CurrentMonth { get; set; }

        /// <summary>
        /// Gets or sets the list of late payment fee configurations.
        /// </summary>
        [BsonElement("latePayments")]
        public List<LatePaymentFeeConfiguration> LatePayments { get; set; }
    }
}
