﻿// <copyright file="SeederBase.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using Bogus;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Services.Identity.Repository;
using NetProGroup.Trust.Application.AppServices.Tools;
using NetProGroup.Trust.DataManager.LegalEntityRelations;
using NetProGroup.Trust.DataManager.LegalEntityRelations.BeneficialOwners.RequestResponses;
using NetProGroup.Trust.Domain.Jurisdictions;
using NetProGroup.Trust.Domain.MasterClients;
using NetProGroup.Trust.Domain.Modules;

namespace NetProGroup.Trust.Application.Seeders.JurisdictionSeeders
{
    public abstract class SeederBase
    {
        /// <summary>
        /// Gets the service provider for dependency injection.
        /// </summary>
        protected IServiceProvider ServiceProvider { get; }

        private readonly ILogger _logger;

        private Faker<SyncBeneficialOwner> _boFaker;
        private Faker<Domain.LegalEntities.LegalEntity> _companyFaker;

        /// <summary>
        /// Initializes a new instance of the <see cref="SeederBase"/> class.
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="serviceProvider"></param>
        public SeederBase(ILogger logger, IServiceProvider serviceProvider)
        {
            _logger = logger;
            ServiceProvider = serviceProvider;

            SetupFakers();
        }

        /// <summary>
        /// Gets a company faker
        /// </summary>
        protected Faker<Domain.LegalEntities.LegalEntity> CompanyFaker => _companyFaker;

        private void SetupFakers()
        {
            _boFaker = BoFaker.Create();
            _companyFaker = new Faker<Domain.LegalEntities.LegalEntity>()
                            .UseSeed(1)
                            .RuleFor(c => c.IncorporationDate, faker => faker.Date.Between(new DateTime(1950, 1, 1), new DateTime(2024, 11, 01)))
                            .RuleFor(owner => owner.ReferralOffice, (faker, owner) => $"REF{faker.Random.Number(1, 6)}");
        }

        /// <summary>
        /// Assigns a module to a jurisdiction.
        /// </summary>
        /// <param name="jurisdictionCode"></param>
        /// <param name="moduleKey"></param>
        protected async Task AssignModuleToJurisdictionAsync(string moduleKey, string jurisdictionCode)
        {
            _logger.LogInformation("Assigning module {ModuleKey} to jurisdiction {JurisdictionCode}", moduleKey, jurisdictionCode);
            var moduleRepository = ServiceProvider.GetRequiredService<IModulesRepository>();
            var module = await moduleRepository.FindFirstOrDefaultByConditionAsync(x => x.Key == moduleKey);

            var jurisdictionRepository = ServiceProvider.GetRequiredService<IJurisdictionsRepository>();
            var jurisdiction = await jurisdictionRepository.FindFirstOrDefaultByConditionAsync(x => x.Code == jurisdictionCode, q => q.Include(x => x.JurisdictionModules));

            if (!jurisdiction.JurisdictionModules.Any(x => x.ModuleId == module.Id))
            {
                jurisdiction.JurisdictionModules.Add(new JurisdictionModule(jurisdiction.Id, module.Id) { IsEnabled = true });
            }
            else
            {
                var jurmod = jurisdiction.JurisdictionModules.FirstOrDefault(x => x.ModuleId == module.Id);
                if (!jurmod.IsEnabled.HasValue)
                {
                    jurmod.IsEnabled = true;
                }
            }

            await jurisdictionRepository.SaveChangesAsync();
        }

        protected void EnableModules(Guid legalEntityId, params string[] modules)
        {
            foreach (var moduleName in modules)
            {
                var modulesRepository = ServiceProvider.GetRequiredService<IModulesRepository>();
                var module = modulesRepository.FindFirstOrDefaultByCondition(x => x.Key == moduleName);

                var repository = ServiceProvider.GetRequiredService<ILegalEntityModulesRepository>();

                var existing = repository.FindFirstOrDefaultByCondition(x => x.LegalEntityId == legalEntityId && x.ModuleId == module.Id);
                if (existing == null)
                {
                    existing = new LegalEntityModule(legalEntityId, module.Id);

                    repository.Insert(existing, saveChanges: false);
                }
                else
                {
                    existing.IsEnabled = true;
                    repository.Update(existing, saveChanges: false);
                }

                existing.IsApproved = true;
                existing.IsEnabled = true;
                repository.SaveChanges();
            }
        }

        protected async Task AssignUsersToMasterClientAsync(string code)
        {
            var repository = ServiceProvider.GetRequiredService<IMasterClientsRepository>();

            var existing = await repository.FindFirstOrDefaultByConditionAsync(x => x.Code == code, q => q.Include(x => x.MasterClientUsers));

            var userRepository = ServiceProvider.GetRequiredService<IUserRepository>();
            var users = (await userRepository.SearchAsync(filter: "<EMAIL>"));

            foreach (var user in users)
            {
                if (!existing.MasterClientUsers.Any(x => x.UserId == user.Id))
                {
                    existing.MasterClientUsers.Add(new MasterClientUser { UserId = user.Id, MasterClient = existing });
                }
            }

            await repository.SaveChangesAsync();
        }

        protected async Task<Guid> CreateMasterClientAsync(string code, bool isActive = true, IServiceProvider serviceProvider = null)
        {
            var repository = (serviceProvider ?? ServiceProvider).GetRequiredService<IMasterClientsRepository>();

            var existing = repository.FindFirstOrDefaultByCondition(x => x.Code == code);
            if (existing == null)
            {
                var toDb = new MasterClient(Guid.NewGuid(), code)
                {
                    IsActive = isActive
                };
                await repository.InsertAsync(toDb, saveChanges: true);
                return toDb.Id;
            }
            else
            {
                existing.IsActive = isActive;
                await repository.UpdateAsync(existing, saveChanges: true);
                return existing.Id;
            }
        }

        protected void CreateBOs(string entityCode)
        {
            var beneficialOwnersDataManager = ServiceProvider.GetRequiredService<IBeneficialOwnersDataManager>();

            var request = new SyncBeneficialOwnerRequest();

            for (var i = 0; i < 20; i++)
            {
                request.ChangedBeneficialOwners.Add(SetupBO(entityCode));
            };

            beneficialOwnersDataManager.SyncBeneficialOwnersAsync(request).Wait();
        }

        protected SyncBeneficialOwner SetupBO(string entityCode)
        {
            var result = _boFaker
                .RuleFor(owner => owner.CompanyNumber, entityCode)
                .RuleFor(owner => owner.EntityCode, entityCode)
                .RuleFor(owner => owner.Code, entityCode)
                .Generate();

            return result;
        }

        protected void CreateDirectors(Guid legalEntityId, string entityCode, string prefix)
        {
            var directorsDataManager = ServiceProvider.GetRequiredService<IDirectorsDataManager>();

            var request = new NetProGroup.Trust.DataManager.LegalEntityRelations.Directors.RequestResponses.SyncDirectorRequest();

            for (var i = 0; i < 20; i++)
            {
                request.ChangedDirectors.Add(SetupDirector(entityCode, prefix, i + 1));
            };

            directorsDataManager.SyncDirectorsAsync(request).Wait();
        }

        protected NetProGroup.Trust.DataManager.LegalEntityRelations.Directors.RequestResponses.SyncDirector SetupDirector(string entityCode, string prefix, int sequence)
        {
            var result = new NetProGroup.Trust.DataManager.LegalEntityRelations.Directors.RequestResponses.SyncDirector
            {
                CompanyNumber = entityCode,
                EntityCode = entityCode,
                Code = entityCode,
                UniqueRelationId = $"Dir-{prefix}-{sequence}",
                Name = $"Dir for {prefix}-{sequence}",
                FileType = sequence > 10 ? "company" : "individual",
                RelationType = "Director ",
            };
            return result;
        }
    }
}
