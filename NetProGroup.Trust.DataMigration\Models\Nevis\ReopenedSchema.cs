using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Nevis
{
    /// <summary>
    /// Represents a reopened schema in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class ReopenedSchema
    {
        /// <summary>
        /// Gets or sets the unique identifier for the reopened schema.
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the list of details for each time the entry was reopened.
        /// </summary>
        [BsonElement("details")]
        public List<ReopenedDetail> Details { get; set; }
    }

    /// <summary>
    /// Represents the details of a single reopening event for an entry in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class ReopenedDetail
    {
        /// <summary>
        /// Gets or sets the unique identifier for the reopened detail.
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        /// <summary>
        /// Gets or sets the date and time when the entry was reopened.
        /// </summary>
        [BsonElement("date_reopened")]
        public DateTime DateReopened { get; set; }

        /// <summary>
        /// Gets or sets the identifier of the user who reopened the entry.
        /// </summary>
        [BsonElement("reopened_by")]
        public string ReopenedBy { get; set; }
    }
}
