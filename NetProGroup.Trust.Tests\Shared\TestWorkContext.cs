﻿using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Services.Identity.Models;

namespace NetProGroup.Trust.Tests.Shared
{
    /// <summary>
    /// Copied from the framework with the only difference that you are allowed to set the IdentityUserId twice.
    /// </summary>
    public class TestWorkContext : IWorkContext
    {
        private readonly IServiceProvider _serviceProvider;
        private Guid? _identityUserId;
        private Dictionary<string, object> _properties;

        /// <summary>Ctor</summary>
        /// <param name="serviceProvider"></param>
        public TestWorkContext(IServiceProvider serviceProvider)
        {
            this._serviceProvider = serviceProvider;
            this._properties = new Dictionary<string, object>((IEqualityComparer<string>)StringComparer.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Gets or sets the IdentityUserId of the signed-on user.
        /// </summary>
        public Guid? IdentityUserId
        {
            get => this._identityUserId;
            set
            {
                if (this._identityUserId.HasValue)
                {
                    Guid? identityUserId = this._identityUserId;
                    Guid empty = Guid.Empty;
                }
                this._identityUserId = value;
            }
        }

        /// <summary>Gets or sets the user found for UserId</summary>
        public ApplicationUserDTO User { get; set; }

        /// <summary>Gets or sets the username of the user</summary>
        public string UserName { get; set; }

        /// <summary>Gets the unique Id for the current context.</summary>
        public Guid? ContextId { get; } = new Guid?(Guid.NewGuid());

        /// <summary>Sets or updates a property.</summary>
        /// <param name="key">Key/Name of the property (case-insensitive).</param>
        /// <param name="value">Value of the property.</param>
        public void SetProperty(string key, object value) => this._properties[key] = value;

        /// <summary>Gets a property, returns null if not exists.</summary>
        /// <param name="key">Key/Name of the property (case-insensitive).</param>
        /// <returns>Value of the property or null if not exists.</returns>
        public object GetProperty(string key)
        {
            object obj;
            return this._properties.TryGetValue(key, out obj) ? obj : (object)null;
        }

        /// <summary>Gets a property, returns null if not exists.</summary>
        /// <typeparam name="T">The generic to cast the value to.</typeparam>
        /// <param name="key">Key/Name of the property (case-insensitive).</param>
        /// <returns>Value of the property or null if not exists.</returns>
        public T GetProperty<T>(string key)
        {
            object obj;
            return this._properties.TryGetValue(key, out obj) ? (T)obj : default(T);
        }
    }
}