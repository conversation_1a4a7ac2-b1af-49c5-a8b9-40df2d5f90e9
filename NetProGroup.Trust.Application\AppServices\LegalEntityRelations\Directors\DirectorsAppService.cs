﻿// <copyright file="DirectorsAppService.cs" company="NetPro Group">
// Copyright (c) NetPro Group. All rights reserved.
// </copyright>

using AutoMapper;
using Microsoft.Extensions.Logging;
using NetProGroup.Framework.Mvc.Contexts;
using NetProGroup.Framework.Tools;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations;
using NetProGroup.Trust.Application.Contracts.LegalEntityRelations.Directors;
using NetProGroup.Trust.DataManager.Auditing;
using NetProGroup.Trust.DataManager.Extensions;
using NetProGroup.Trust.DataManager.LegalEntityRelations;
using NetProGroup.Trust.DataManager.Security;
using NetProGroup.Trust.Domain.Modules;
using NetProGroup.Trust.Domain.Shared.Modules;
using X.PagedList;

namespace NetProGroup.Trust.Application.LegalEntityRelations.Directors
{
    /// <summary>
    /// Application service for Directors.
    /// </summary>
    public class DirectorsAppService : IDirectorsAppService
    {
        private readonly ILogger _logger;
        private readonly IMapper _mapper;
        private readonly IWorkContext _workContext;
        private readonly ISystemAuditManager _systemAuditManager;
        private readonly IDirectorsDataManager _dataManager;
        private readonly ISecurityManager _securityManager;
        private readonly IModulesRepository _modulesRepository;
        private readonly ILegalEntityModulesRepository _legalEntitiesModulesRepository;

        /// <summary>
        /// Initializes a new instance of the <see cref="DirectorsAppService"/> class.
        /// </summary>
        /// <param name="logger">Instance of the logger.</param>
        /// <param name="mapper">Instance of the mapper.</param>
        /// <param name="workContext">The current workcontext.</param>
        /// <param name="systemAuditManager">The AuditManager for logging activities.</param>
        /// <param name="dataManager">The DataManager to use.</param>
        /// <param name="securityManager">The security manager.</param>
        /// <param name="modulesRepository">The repository for modules.</param>
        /// <param name="legalEntitiesModulesRepository">The repository for legal entities modules.</param>
        public DirectorsAppService(ILogger<DirectorsAppService> logger,
                                   IMapper mapper,
                                   IWorkContext workContext,
                                   ISystemAuditManager systemAuditManager,
                                   IDirectorsDataManager dataManager,
                                   ISecurityManager securityManager,
                                   IModulesRepository modulesRepository,
                                   ILegalEntityModulesRepository legalEntitiesModulesRepository)
        {
            _logger = logger;
            _mapper = mapper;
            _workContext = workContext;
            _systemAuditManager = systemAuditManager;
            _dataManager = dataManager;
            _securityManager = securityManager;
            _modulesRepository = modulesRepository;
            _legalEntitiesModulesRepository = legalEntitiesModulesRepository;
        }

        /// <inheritdoc/>
        public async Task<DirectorDTO> GetDirectorAsync(string uniqueRelationId)
        {
            Check.NotNullOrWhiteSpace(uniqueRelationId, nameof(uniqueRelationId));

            await CheckModuleEnabledAndAccessToCompany(uniqueRelationId);

            var directorDto = await _dataManager.GetDirectorAsync(uniqueRelationId);

            return directorDto;
        }

        /// <inheritdoc/>
        public async Task<IPagedList<DirectorDTO>> ListDirectorsAsync(Guid legalEntityId, int pageNumber, int pageSize)
        {
            await _securityManager.RequireClientAccessToCompanyAsync(legalEntityId);
            await CheckModuleEnabledForLegalEntity(legalEntityId);

            var request = new DataManager.LegalEntityRelations.Directors.RequestResponses.ListDirectorsRequest { LegalEntityId = legalEntityId, PageNumber = pageNumber, PageSize = pageSize };
            var response = await _dataManager.ListDirectorsAsync(request);

            return response.DirectorItems;
        }

        /// <inheritdoc/>
        public async Task<DirectorComparisonDTO> GetDirectorForComparisonAsync(string uniqueRelationId)
        {
            Check.NotNullOrWhiteSpace(uniqueRelationId, nameof(uniqueRelationId));

            await CheckModuleEnabledAndAccessToCompany(uniqueRelationId);

            var directorComparisonDto = await _dataManager.GetDirectorForComparisonAsync(uniqueRelationId);

            return directorComparisonDto;
        }

        /// <inheritdoc/>
        public async Task<DirectorDTO> RequestUpdateAsync(RequestUpdateDTO requestUpdate)
        {
            ArgumentNullException.ThrowIfNull(requestUpdate, nameof(requestUpdate));

            await CheckModuleEnabledAndAccessToCompany(requestUpdate.UniqueRelationId);

            var request = new DataManager.LegalEntityRelations.RequestResponses.RequestUpdateRequest
            {
                UniqueRelationId = requestUpdate.UniqueRelationId,
                UserId = _workContext.IdentityUserId.Value,
                UpdateRequestType = requestUpdate.UpdateRequestType,
                UpdateRequestComments = requestUpdate.UpdateRequestComments
            };

            var response = await _dataManager.RequestUpdateAsync(request);

            return await _dataManager.GetDirectorAsync(requestUpdate.UniqueRelationId);
        }

        /// <inheritdoc/>
        public async Task RequestAssistanceAsync(RequestAssistanceDTO requestAssistance)
        {
            ArgumentNullException.ThrowIfNull(requestAssistance, nameof(requestAssistance));

            await _securityManager.RequireClientAccessToCompanyAsync(requestAssistance.LegalEntityId);
            await CheckModuleEnabledForLegalEntity(requestAssistance.LegalEntityId);

            var request = new DataManager.LegalEntityRelations.RequestResponses.RequestAssistanceRequest
            {
                LegalEntityId = requestAssistance.LegalEntityId,
                UserId = _workContext.IdentityUserId.Value,
                AssistanceRequestType = requestAssistance.AssistanceRequestType,
                AssistanceRequestComments = requestAssistance.AssistanceRequestComments
            };

            var response = await _dataManager.RequestAssistanceAsync(request);

            return;
        }

        /// <inheritdoc/>
        public async Task<DirectorDTO> SetConfirmationAsync(string uniqueRelationId)
        {
            Check.NotNullOrWhiteSpace(uniqueRelationId, nameof(uniqueRelationId));

            await CheckModuleEnabledAndAccessToCompany(uniqueRelationId);

            var request = new DataManager.LegalEntityRelations.RequestResponses.ConfirmationRequest
            {
                UniqueRelationId = uniqueRelationId,
                UserId = _workContext.IdentityUserId.Value
            };

            var response = await _dataManager.ConfirmDataAsync(request);

            return await _dataManager.GetDirectorAsync(uniqueRelationId);
        }

        public async Task SimulateUpdateSync(string uniqueRelationId, string newName)
        {
            Check.NotNullOrWhiteSpace(uniqueRelationId, nameof(uniqueRelationId));

            await CheckModuleEnabledAndAccessToCompany(uniqueRelationId);

            var current = await _dataManager.GetDirectorAsync(uniqueRelationId);

            var request = new DataManager.LegalEntityRelations.Directors.RequestResponses.SyncDirectorRequest();

            request.ChangedDirectors.Add(new DataManager.LegalEntityRelations.Directors.RequestResponses.SyncDirector
            {
                Name = newName,
                RelationType = current.OfficerTypeName,
                OfficerTypeName = current.OfficerTypeName,
                UniqueRelationId = current.UniqueRelationCode,
                Nationality = current.Nationality,
                TIN = current.TIN,
                FileType = current.IsIndividual ? "individual" : "company",
                DateOfBirthOrIncorp = current.IsIndividual ? current.DateOfBirth.GetValueOrDefault() : current.DateOfIncorporation.GetValueOrDefault(),
                PlaceOfBirthOrIncorp = current.IsIndividual ? current.PlaceOfBirth : current.IncorporationPlace,
                CountryOfBirthOrIncorp = current.IsIndividual ? current.CountryOfBirth : current.IncorporationCountry,
                CountryCodeOfBirthOrIncorp = current.IsIndividual ? current.CountryCodeOfBirth : current.IncorporationCountryCode,
                ResidentialOrRegisteredAddress = current.IsIndividual ? current.ResidentialAddress : current.Address,
                FromDate = current.AppointmentDate.GetValueOrDefault(),
                ToDate = current.CessationDate.GetValueOrDefault(),
                IncorporationNumberOrPassportNr = current.IncorporationNumber
            });

            await _dataManager.SyncDirectorsAsync(request);
        }

        private async Task CheckModuleEnabledForLegalEntity(Guid legalEntityId)
        {
            var module = await _modulesRepository.FindFirstOrDefaultByConditionAsync(m => m.Key == ModuleKeyConsts.BODirectors);
            await _legalEntitiesModulesRepository.CheckModuleEnabledForLegalEntityAsync(legalEntityId, module.Id);
        }

        private async Task CheckModuleEnabledAndAccessToCompany(string uniqueRelationId)
        {
            var director = await _dataManager.FindDirectorAsync(uniqueRelationId);
            await _securityManager.RequireClientAccessToCompanyAsync(director.LegalEntityId);

            await CheckModuleEnabledForLegalEntity(director.LegalEntityId);
        }
    }
}
