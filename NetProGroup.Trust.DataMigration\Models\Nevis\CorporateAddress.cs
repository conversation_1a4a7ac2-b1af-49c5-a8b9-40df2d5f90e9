using MongoDB.Bson.Serialization.Attributes;

namespace NetProGroup.Trust.DataMigration.Models.Nevis
{
    /// <summary>
    /// Represents a corporate address in the old database.
    /// </summary>
    [BsonIgnoreExtraElements] // Ignore any extra elements in the BSON document that are not mapped to properties
    public class CorporateAddress
    {
        /// <summary>
        /// Gets or sets a value indicating whether the records are kept in Nevis.
        /// </summary>
        [BsonElement("keep_registered_office")]
        public bool KeepRegisteredOffice { get; set; }

        /// <summary>
        /// Gets or sets the address.
        /// </summary>
        [BsonElement("address")]
        public string Address { get; set; }
    }
}
